import { OpenAI } from 'openai';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { getBaseUrl, getApiKey } from './config.js';
import { NetworkError } from '../types/index.js';

export interface OpenAIClientOptions {
  provider?: string;
  apiKey?: string;
  baseURL?: string;
  timeout?: number;
  organization?: string;
  project?: string;
  maxRetries?: number;
}

/**
 * Create an OpenAI client instance for the specified provider
 */
export function createOpenAIClient(options: OpenAIClientOptions = {}): OpenAI {
  const {
    provider = "openai",
    apiKey,
    baseURL,
    timeout = 30000,
    organization,
    project,
    maxRetries = 3
  } = options;

  // Get API key with fallback mechanisms
  const resolvedApiKey = apiKey || getApiKey(provider);
  if (!resolvedApiKey) {
    throw new NetworkError(`No API key found for provider: ${provider}. Please set the appropriate environment variable.`);
  }

  // Get base URL with environment variable override support
  const resolvedBaseURL = baseURL || getBaseUrl(provider);

  // Configure proxy if available
  const proxyUrl = process.env.HTTPS_PROXY || process.env.https_proxy;
  const httpAgent = proxyUrl ? new HttpsProxyAgent(proxyUrl) : undefined;

  // Handle Azure OpenAI specific configuration
  if (provider === 'azure') {
    return createAzureOpenAIClient({
      apiKey: resolvedApiKey,
      baseURL: resolvedBaseURL,
      timeout,
      httpAgent,
      maxRetries
    });
  }

  // Create standard OpenAI client
  const clientConfig: any = {
    apiKey: resolvedApiKey,
    baseURL: resolvedBaseURL,
    timeout,
    maxRetries,
    httpAgent
  };

  // Add organization and project if provided
  if (organization) {
    clientConfig.organization = organization;
  }
  
  if (project) {
    clientConfig.project = project;
  }

  return new OpenAI(clientConfig);
}

/**
 * Create Azure OpenAI client with specific configuration
 */
function createAzureOpenAIClient(options: {
  apiKey: string;
  baseURL: string;
  timeout: number;
  httpAgent?: any;
  maxRetries: number;
}): OpenAI {
  const { apiKey, baseURL, timeout, httpAgent, maxRetries } = options;

  // Azure OpenAI uses a different API version and endpoint structure
  const azureConfig = {
    apiKey,
    baseURL,
    timeout,
    maxRetries,
    httpAgent,
    defaultQuery: {
      'api-version': process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview'
    },
    defaultHeaders: {
      'api-key': apiKey
    }
  };

  return new OpenAI(azureConfig);
}

/**
 * Test connection to the OpenAI API
 */
export async function testConnection(provider: string = "openai"): Promise<boolean> {
  try {
    const client = createOpenAIClient({ provider });
    
    // Try to list models as a connection test
    await client.models.list();
    return true;
  } catch (error) {
    console.error(`Connection test failed for provider ${provider}:`, error);
    return false;
  }
}

/**
 * Get available models from the provider
 */
export async function getAvailableModels(provider: string = "openai"): Promise<string[]> {
  try {
    const client = createOpenAIClient({ provider });
    const response = await client.models.list();
    
    return response.data
      .map(model => model.id)
      .filter(id => id.includes('gpt') || id.includes('claude') || id.includes('gemini'))
      .sort();
  } catch (error) {
    console.warn(`Failed to fetch models for provider ${provider}:`, error);
    return [];
  }
}

/**
 * Validate API key format for different providers
 */
export function validateApiKey(provider: string, apiKey: string): boolean {
  if (!apiKey) return false;

  switch (provider.toLowerCase()) {
    case 'openai':
      return apiKey.startsWith('sk-') && apiKey.length > 20;
    
    case 'azure':
      return apiKey.length >= 32; // Azure keys are typically 32+ characters
    
    case 'gemini':
      return apiKey.startsWith('AIza') && apiKey.length === 39;
    
    case 'mistral':
      return apiKey.length >= 32;
    
    case 'deepseek':
      return apiKey.startsWith('sk-') && apiKey.length > 20;
    
    case 'xai':
      return apiKey.startsWith('xai-') && apiKey.length > 20;
    
    case 'groq':
      return apiKey.startsWith('gsk_') && apiKey.length > 20;
    
    case 'openrouter':
      return apiKey.startsWith('sk-or-') && apiKey.length > 20;
    
    default:
      // For unknown providers, just check it's not empty and has reasonable length
      return apiKey.length >= 10;
  }
}

/**
 * Get provider-specific headers
 */
export function getProviderHeaders(provider: string): Record<string, string> {
  const headers: Record<string, string> = {};

  switch (provider.toLowerCase()) {
    case 'openrouter':
      headers['HTTP-Referer'] = 'https://kritrima-ai.com';
      headers['X-Title'] = 'Kritrima AI CLI';
      break;
    
    case 'azure':
      headers['Content-Type'] = 'application/json';
      break;
    
    default:
      headers['Content-Type'] = 'application/json';
      break;
  }

  return headers;
}

/**
 * Handle rate limiting with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Don't retry on certain errors
      if (error.status === 401 || error.status === 403) {
        throw error;
      }

      // Only retry on rate limit or server errors
      if (error.status !== 429 && error.status < 500) {
        throw error;
      }

      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Create a streaming completion with proper error handling
 */
export async function createStreamingCompletion(
  client: OpenAI,
  params: OpenAI.ChatCompletionCreateParams
): Promise<AsyncIterable<OpenAI.ChatCompletionChunk>> {
  return withRetry(async () => {
    return await client.chat.completions.create({
      ...params,
      stream: true
    });
  });
}
