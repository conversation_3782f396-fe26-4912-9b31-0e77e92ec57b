import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { ApprovalPolicy } from '../../types';

interface ApprovalModeOverlayProps {
  currentMode: ApprovalPolicy;
  onModeChange: (mode: ApprovalPolicy) => void;
  onClose: () => void;
}

const APPROVAL_MODES: Array<{
  mode: ApprovalPolicy;
  name: string;
  description: string;
  color: string;
  icon: string;
  details: string[];
}> = [
  {
    mode: 'suggest',
    name: 'Suggest Mode',
    description: 'Manual approval required for all commands',
    color: 'red',
    icon: '🔒',
    details: [
      '• All commands require explicit approval',
      '• Maximum security and control',
      '• Best for sensitive environments',
      '• Recommended for production systems'
    ]
  },
  {
    mode: 'auto-edit',
    name: 'Auto-Edit Mode',
    description: 'Auto-approve safe commands, ask for dangerous ones',
    color: 'yellow',
    icon: '⚡',
    details: [
      '• Safe read-only commands auto-approved',
      '• File modifications and system commands require approval',
      '• Balanced security and convenience',
      '• Good for development environments'
    ]
  },
  {
    mode: 'full-auto',
    name: 'Full-Auto Mode',
    description: 'Auto-approve all commands (use with caution)',
    color: 'green',
    icon: '🚀',
    details: [
      '• All commands executed automatically',
      '• Maximum speed and convenience',
      '• Commands still run in sandbox when available',
      '• Use only in trusted, isolated environments'
    ]
  }
];

export function ApprovalModeOverlay({ currentMode, onModeChange, onClose }: ApprovalModeOverlayProps) {
  const [selectedIndex, setSelectedIndex] = useState(
    APPROVAL_MODES.findIndex(mode => mode.mode === currentMode)
  );

  useInput((input, key) => {
    if (key.escape) {
      onClose();
      return;
    }

    if (key.return) {
      const selectedMode = APPROVAL_MODES[selectedIndex];
      if (selectedMode) {
        onModeChange(selectedMode.mode);
      }
      return;
    }

    if (key.upArrow) {
      setSelectedIndex(prev => 
        prev > 0 ? prev - 1 : APPROVAL_MODES.length - 1
      );
      return;
    }

    if (key.downArrow) {
      setSelectedIndex(prev => 
        prev < APPROVAL_MODES.length - 1 ? prev + 1 : 0
      );
      return;
    }

    // Quick selection by number
    const num = parseInt(input);
    if (num >= 1 && num <= APPROVAL_MODES.length) {
      setSelectedIndex(num - 1);
      return;
    }
  });

  const selectedMode = APPROVAL_MODES[selectedIndex];

  return (
    <Box flexDirection="column" height="100%" padding={2}>
      {/* Header */}
      <Box marginBottom={2}>
        <Text color="cyan" bold>
          🔐 Command Approval Mode
        </Text>
      </Box>

      {/* Current Mode */}
      <Box marginBottom={2} paddingX={2} borderStyle="round">
        <Text>
          Current mode: <Text color="cyan" bold>{currentMode}</Text>
        </Text>
      </Box>

      {/* Mode List */}
      <Box flexDirection="column" flexGrow={1}>
        {APPROVAL_MODES.map((mode, index) => (
          <Box key={mode.mode} flexDirection="column" marginBottom={2}>
            <Box>
              <Text color={index === selectedIndex ? 'cyan' : mode.color as any} bold>
                {index === selectedIndex ? '▶ ' : '  '}
                {mode.icon} {mode.name}
                {mode.mode === currentMode && ' (current)'}
              </Text>
            </Box>
            
            <Box paddingLeft={4}>
              <Text color="gray">
                {mode.description}
              </Text>
            </Box>
            
            {index === selectedIndex && (
              <Box flexDirection="column" paddingLeft={4} marginTop={1}>
                {mode.details.map((detail, detailIndex) => (
                  <Text key={detailIndex} color="gray" dimColor>
                    {detail}
                  </Text>
                ))}
              </Box>
            )}
          </Box>
        ))}
      </Box>

      {/* Security Warning */}
      {selectedMode && selectedMode.mode === 'full-auto' && (
        <Box marginY={2} paddingX={2} borderStyle="round">
          <Text color="red" bold>
            ⚠️  WARNING: Full-Auto Mode
          </Text>
          <Text color="red">
            This mode will execute all AI-suggested commands automatically.
            Only use in trusted, isolated environments.
          </Text>
        </Box>
      )}

      {/* Footer */}
      <Box marginTop={2} flexDirection="column">
        <Text color="gray">
          Use ↑↓ to navigate, Enter to select, Esc to cancel
        </Text>
        <Text color="gray" dimColor>
          You can also press 1-{APPROVAL_MODES.length} for quick selection
        </Text>
      </Box>
    </Box>
  );
}
