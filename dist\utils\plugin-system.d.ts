import { AppConfig, ResponseInputItem } from '../types';
/**
 * Plugin lifecycle hooks
 */
export type PluginHook = 'beforeMessage' | 'afterMessage' | 'beforeCommand' | 'afterCommand' | 'onError' | 'onModelSwitch' | 'onSessionStart' | 'onSessionEnd';
/**
 * Plugin context
 */
export interface PluginContext {
    config: AppConfig;
    sessionId?: string;
    userId?: string;
    workdir: string;
    version: string;
}
/**
 * Plugin hook data
 */
export interface PluginHookData {
    hook: PluginHook;
    data: any;
    context: PluginContext;
    timestamp: number;
}
/**
 * Plugin interface
 */
export interface Plugin {
    name: string;
    version: string;
    description: string;
    author: string;
    hooks: PluginHook[];
    dependencies?: string[];
    config?: Record<string, any>;
    initialize?(context: PluginContext): Promise<void> | void;
    destroy?(): Promise<void> | void;
    beforeMessage?(message: string, context: PluginContext): Promise<string> | string;
    afterMessage?(response: ResponseInputItem[], context: PluginContext): Promise<ResponseInputItem[]> | ResponseInputItem[];
    beforeCommand?(command: string[], context: PluginContext): Promise<string[]> | string[];
    afterCommand?(result: any, context: PluginContext): Promise<any> | any;
    onError?(error: Error, context: PluginContext): Promise<void> | void;
    onModelSwitch?(from: string, to: string, context: PluginContext): Promise<void> | void;
    onSessionStart?(sessionId: string, context: PluginContext): Promise<void> | void;
    onSessionEnd?(sessionId: string, context: PluginContext): Promise<void> | void;
}
/**
 * Plugin manifest
 */
export interface PluginManifest {
    name: string;
    version: string;
    description: string;
    author: string;
    main: string;
    hooks: PluginHook[];
    dependencies?: string[];
    config?: Record<string, any>;
    permissions?: string[];
    minVersion?: string;
    maxVersion?: string;
}
/**
 * Plugin manager class
 */
export declare class PluginManager {
    private plugins;
    private pluginDir;
    private context;
    private enabled;
    constructor(context: PluginContext, customPluginDir?: string);
    /**
     * Initialize plugin system
     */
    initialize(): Promise<void>;
    /**
     * Load all plugins from plugin directory
     */
    loadPlugins(): Promise<void>;
    /**
     * Load a specific plugin
     */
    loadPlugin(pluginName: string): Promise<boolean>;
    /**
     * Unload a plugin
     */
    unloadPlugin(pluginName: string): Promise<boolean>;
    /**
     * Execute hook for all plugins
     */
    executeHook(hook: PluginHook, data: any): Promise<any>;
    /**
     * Get plugin hook method
     */
    private getHookMethod;
    /**
     * List loaded plugins
     */
    listPlugins(): Array<{
        name: string;
        version: string;
        description: string;
        hooks: PluginHook[];
    }>;
    /**
     * Get plugin by name
     */
    getPlugin(name: string): Plugin | undefined;
    /**
     * Check if plugin is loaded
     */
    isPluginLoaded(name: string): boolean;
    /**
     * Enable/disable plugin system
     */
    setEnabled(enabled: boolean): void;
    /**
     * Update plugin context
     */
    updateContext(context: Partial<PluginContext>): void;
    /**
     * Validate plugin manifest
     */
    private validateManifest;
    /**
     * Validate plugin implementation
     */
    private validatePlugin;
    /**
     * Check version compatibility
     */
    private isVersionCompatible;
    /**
     * Install plugin from package
     */
    installPlugin(packagePath: string): Promise<boolean>;
    /**
     * Uninstall plugin
     */
    uninstallPlugin(pluginName: string): Promise<boolean>;
    /**
     * Shutdown plugin system
     */
    shutdown(): Promise<void>;
}
/**
 * Create example plugin template
 */
export declare function createPluginTemplate(name: string, description: string): {
    manifest: PluginManifest;
    plugin: string;
};
//# sourceMappingURL=plugin-system.d.ts.map