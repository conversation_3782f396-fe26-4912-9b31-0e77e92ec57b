import { AppConfig } from '../types';
/**
 * Supported image formats for multimodal AI
 */
export declare const SUPPORTED_IMAGE_FORMATS: string[];
/**
 * Supported document formats for analysis
 */
export declare const SUPPORTED_DOCUMENT_FORMATS: string[];
/**
 * Maximum file size for uploads (10MB)
 */
export declare const MAX_FILE_SIZE: number;
/**
 * File upload result
 */
export interface FileUploadResult {
    success: boolean;
    data?: string;
    mimeType?: string;
    size?: number;
    error?: string;
}
/**
 * Multimodal content item
 */
export interface MultimodalContent {
    type: 'text' | 'image_url' | 'document';
    text?: string;
    image_url?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
    };
    document?: {
        content: string;
        filename: string;
        mimeType: string;
    };
}
/**
 * Process file for multimodal AI input
 */
export declare function processFileForAI(filePath: string, config: AppConfig): Promise<FileUploadResult>;
/**
 * Create multimodal content from file
 */
export declare function createMultimodalContent(filePath: string, config: AppConfig, detail?: 'low' | 'high' | 'auto'): Promise<MultimodalContent | null>;
/**
 * Extract file references from user input
 */
export declare function extractFileReferences(input: string): string[];
/**
 * Process user input with file references
 */
export declare function processInputWithFiles(input: string, config: AppConfig): Promise<{
    text: string;
    multimodalContent: MultimodalContent[];
    errors: string[];
}>;
/**
 * Validate file for AI processing
 */
export declare function validateFileForAI(filePath: string): Promise<{
    valid: boolean;
    error?: string;
    mimeType?: string;
    size?: number;
}>;
/**
 * Get file type description for user
 */
export declare function getFileTypeDescription(mimeType: string): string;
/**
 * Format file size for display
 */
export declare function formatFileSize(bytes: number): string;
//# sourceMappingURL=multimodal.d.ts.map