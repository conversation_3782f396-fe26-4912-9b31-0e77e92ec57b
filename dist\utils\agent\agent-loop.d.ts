import { AppConfig, ResponseItem, ApprovalRequest } from '../../types/index.js';
export interface AgentLoopOptions {
    onProgress?: (partialResponse: string) => void;
    onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>;
    signal?: AbortSignal;
}
/**
 * Core agent loop that orchestrates AI interactions with tool calling
 */
export declare class AgentLoop {
    private model;
    private provider;
    private oai;
    private config;
    private pendingAborts;
    private pluginManager?;
    constructor(config: AppConfig);
    /**
     * Initialize plugin manager
     */
    private initializePluginManager;
    /**
     * Process a user message and return AI response
     */
    processMessage(message: string, conversationHistory?: ResponseItem[], options?: AgentLoopOptions): Promise<ResponseItem | null>;
    /**
     * Handle function calls in AI response
     */
    private handleFunctionCalls;
    /**
     * Execute a function call
     */
    private executeFunctionCall;
    /**
     * Build conversation context from history and current input
     */
    private buildConversationContext;
    /**
     * Get system prompt for the AI
     */
    private getSystemPrompt;
    /**
     * Get available tools for the AI
     */
    private getAvailableTools;
    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<AppConfig>): void;
    /**
     * Get current configuration
     */
    getConfig(): AppConfig;
    /**
     * Clear conversation history
     */
    clearHistory(): void;
    /**
     * Abort pending operations
     */
    abort(): void;
    /**
     * Estimate token count for response
     */
    private estimateTokenCount;
    /**
     * Estimate response size in bytes
     */
    private estimateResponseSize;
    /**
     * Shutdown agent loop and cleanup resources
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=agent-loop.d.ts.map