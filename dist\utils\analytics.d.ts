/**
 * Analytics event types
 */
export type AnalyticsEventType = 'app_start' | 'app_exit' | 'message_sent' | 'message_received' | 'command_executed' | 'file_processed' | 'model_switched' | 'provider_switched' | 'error_occurred' | 'session_created' | 'session_loaded' | 'approval_requested' | 'approval_granted' | 'approval_denied';
/**
 * Analytics event data
 */
export interface AnalyticsEvent {
    id: string;
    type: AnalyticsEventType;
    timestamp: number;
    sessionId?: string;
    userId?: string;
    data: Record<string, any>;
    duration?: number;
    success?: boolean;
}
/**
 * Performance metrics
 */
export interface PerformanceMetrics {
    responseTime: number;
    tokenCount: number;
    requestSize: number;
    responseSize: number;
    memoryUsage: number;
    cpuUsage?: number;
}
/**
 * Usage statistics
 */
export interface UsageStats {
    totalSessions: number;
    totalMessages: number;
    totalCommands: number;
    totalTokens: number;
    averageResponseTime: number;
    mostUsedModel: string;
    mostUsedProvider: string;
    errorRate: number;
    approvalRate: number;
}
/**
 * Analytics configuration
 */
export interface AnalyticsConfig {
    enabled: boolean;
    collectPerformanceMetrics: boolean;
    collectUsageStats: boolean;
    maxEvents: number;
    flushInterval: number;
    anonymize: boolean;
}
/**
 * Analytics manager class
 */
export declare class AnalyticsManager {
    private config;
    private events;
    private metricsBuffer;
    private dataDir;
    private flushTimer?;
    constructor(config?: Partial<AnalyticsConfig>);
    /**
     * Initialize analytics system
     */
    private initialize;
    /**
     * Track an analytics event
     */
    trackEvent(type: AnalyticsEventType, data?: Record<string, any>, options?: {
        sessionId?: string;
        userId?: string;
        duration?: number;
        success?: boolean;
    }): void;
    /**
     * Track performance metrics
     */
    trackPerformance(metrics: PerformanceMetrics): void;
    /**
     * Get usage statistics
     */
    getUsageStats(): UsageStats;
    /**
     * Get performance summary
     */
    getPerformanceSummary(): {
        averageResponseTime: number;
        p95ResponseTime: number;
        averageTokenCount: number;
        averageMemoryUsage: number;
        totalRequests: number;
    };
    /**
     * Export analytics data
     */
    exportData(format?: 'json' | 'csv'): Promise<string>;
    /**
     * Flush data to disk
     */
    flush(): Promise<void>;
    /**
     * Clean up old analytics files
     */
    cleanup(olderThanDays?: number): Promise<void>;
    /**
     * Shutdown analytics system
     */
    shutdown(): Promise<void>;
    /**
     * Generate unique event ID
     */
    private generateEventId;
    /**
     * Anonymize user ID
     */
    private anonymizeUserId;
    /**
     * Anonymize sensitive data
     */
    private anonymizeData;
}
/**
 * Global analytics instance
 */
export declare const analytics: AnalyticsManager;
/**
 * Convenience functions for common tracking scenarios
 */
export declare const trackMessage: (data: Record<string, any>, options?: any) => void;
export declare const trackCommand: (data: Record<string, any>, options?: any) => void;
export declare const trackError: (error: Error, data?: Record<string, any>) => void;
export declare const trackModelSwitch: (from: string, to: string, provider: string) => void;
export declare const trackPerformance: (metrics: PerformanceMetrics) => void;
//# sourceMappingURL=analytics.d.ts.map