import { describe, test, expect } from '@jest/globals';
import { getCurrentPlatform, adaptCommand } from '../utils/agent/platform-commands.js';
import { validateImageFile, formatFileSize, isImageFile } from '../utils/input-utils.js';
import { getSandboxCapabilities, isSandboxAvailable } from '../utils/agent/sandbox/index.js';

describe('Platform Commands', () => {
  test('getCurrentPlatform should return valid platform', () => {
    const platform = getCurrentPlatform();
    expect(['windows', 'unix']).toContain(platform);
  });

  test('adaptCommand should handle empty commands', () => {
    const result = adaptCommand([]);
    expect(result).toEqual([]);
  });

  test('adaptCommand should adapt ls command on Windows', () => {
    // Mock platform detection for testing
    const originalPlatform = process.platform;
    Object.defineProperty(process, 'platform', {
      value: 'win32'
    });

    const result = adaptCommand(['ls', '-la']);
    expect(result[0]).toBe('dir');

    // Restore original platform
    Object.defineProperty(process, 'platform', {
      value: originalPlatform
    });
  });
});

describe('Input Utils', () => {
  test('formatFileSize should format bytes correctly', () => {
    expect(formatFileSize(1024)).toBe('1.0 KB');
    expect(formatFileSize(1048576)).toBe('1.0 MB');
    expect(formatFileSize(500)).toBe('500.0 B');
  });

  test('isImageFile should detect image files', () => {
    expect(isImageFile('test.jpg')).toBe(true);
    expect(isImageFile('test.png')).toBe(true);
    expect(isImageFile('test.txt')).toBe(false);
  });

  test('validateImageFile should handle non-existent files', () => {
    const result = validateImageFile('non-existent-file.jpg');
    expect(result.valid).toBe(false);
    expect(result.error).toBe('File not found');
  });
});

describe('Sandbox System', () => {
  test('getSandboxCapabilities should return valid capabilities', () => {
    const capabilities = getSandboxCapabilities();
    expect(capabilities).toHaveProperty('sandboxed');
    expect(capabilities).toHaveProperty('networkRestricted');
    expect(capabilities).toHaveProperty('filesystemRestricted');
    expect(capabilities).toHaveProperty('processRestricted');
    expect(capabilities).toHaveProperty('name');
    expect(capabilities).toHaveProperty('securityLevel');
  });

  test('isSandboxAvailable should return boolean', () => {
    const available = isSandboxAvailable();
    expect(typeof available).toBe('boolean');
  });
});

describe('Error Handling', () => {
  test('should handle errors gracefully', () => {
    // Test that functions don't throw unexpected errors
    expect(() => getCurrentPlatform()).not.toThrow();
    expect(() => getSandboxCapabilities()).not.toThrow();
    expect(() => formatFileSize(1024)).not.toThrow();
  });
});
