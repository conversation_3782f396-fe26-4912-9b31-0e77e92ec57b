import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { getProviderNames, getProviderModels } from '../../utils/providers.js';
import { fetchModels } from '../../utils/model-utils.js';

interface ModelOverlayProps {
  currentModel: string;
  currentProvider: string;
  onModelChange: (model: string, provider: string) => void;
  onClose: () => void;
}

type TabType = 'providers' | 'models';

export function ModelOverlay({ currentModel, currentProvider, onModelChange, onClose }: ModelOverlayProps) {
  const [activeTab, setActiveTab] = useState<TabType>('providers');
  const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
  const [selectedModelIndex, setSelectedModelIndex] = useState(0);
  const [providers] = useState(getProviderNames());
  const [models, setModels] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize selected provider index
  useEffect(() => {
    const currentIndex = providers.indexOf(currentProvider);
    if (currentIndex !== -1) {
      setSelectedProviderIndex(currentIndex);
    }
  }, [providers, currentProvider]);

  // Load models when provider changes
  useEffect(() => {
    loadModelsForProvider(providers[selectedProviderIndex]);
  }, [selectedProviderIndex, providers]);

  const loadModelsForProvider = async (provider: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Try to fetch models from API
      const fetchedModels = await fetchModels(provider);
      setModels(fetchedModels);
      
      // Set selected model index
      const currentIndex = fetchedModels.indexOf(currentModel);
      setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
    } catch (err) {
      // Fallback to predefined models
      const fallbackModels = getProviderModels(provider);
      setModels(fallbackModels);
      setError('Failed to fetch models from API, showing predefined models');
      
      const currentIndex = fallbackModels.indexOf(currentModel);
      setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
    } finally {
      setLoading(false);
    }
  };

  useInput((input, key) => {
    if (key.escape) {
      onClose();
      return;
    }

    if (key.tab) {
      setActiveTab(prev => prev === 'providers' ? 'models' : 'providers');
      return;
    }

    if (key.return) {
      if (activeTab === 'providers') {
        setActiveTab('models');
      } else {
        // Select model
        const selectedProvider = providers[selectedProviderIndex];
        const selectedModel = models[selectedModelIndex];
        if (selectedModel) {
          onModelChange(selectedModel, selectedProvider);
        }
      }
      return;
    }

    if (key.upArrow) {
      if (activeTab === 'providers') {
        setSelectedProviderIndex(prev => 
          prev > 0 ? prev - 1 : providers.length - 1
        );
      } else {
        setSelectedModelIndex(prev => 
          prev > 0 ? prev - 1 : models.length - 1
        );
      }
      return;
    }

    if (key.downArrow) {
      if (activeTab === 'providers') {
        setSelectedProviderIndex(prev => 
          prev < providers.length - 1 ? prev + 1 : 0
        );
      } else {
        setSelectedModelIndex(prev => 
          prev < models.length - 1 ? prev + 1 : 0
        );
      }
      return;
    }
  });

  return (
    <Box flexDirection="column" height="100%" padding={2}>
      {/* Header */}
      <Box marginBottom={2}>
        <Text color="cyan" bold>
          🤖 Model & Provider Selection
        </Text>
      </Box>

      {/* Tabs */}
      <Box marginBottom={2}>
        <Box marginRight={4}>
          <Text color={activeTab === 'providers' ? 'cyan' : 'gray'} bold>
            {activeTab === 'providers' ? '▶ ' : '  '}Providers
          </Text>
        </Box>
        <Box>
          <Text color={activeTab === 'models' ? 'cyan' : 'gray'} bold>
            {activeTab === 'models' ? '▶ ' : '  '}Models
          </Text>
        </Box>
      </Box>

      {/* Content */}
      <Box flexDirection="row" flexGrow={1}>
        {/* Providers Column */}
        <Box flexDirection="column" width="50%" marginRight={2}>
          <Box marginBottom={1}>
            <Text color="yellow" bold>
              Providers:
            </Text>
          </Box>
          {providers.map((provider, index) => (
            <Box key={provider} marginBottom={1}>
              <Text color={
                index === selectedProviderIndex 
                  ? (activeTab === 'providers' ? 'cyan' : 'blue')
                  : 'gray'
              }>
                {index === selectedProviderIndex ? '▶ ' : '  '}
                {provider}
                {provider === currentProvider && ' (current)'}
              </Text>
            </Box>
          ))}
        </Box>

        {/* Models Column */}
        <Box flexDirection="column" width="50%">
          <Box marginBottom={1}>
            <Text color="yellow" bold>
              Models for {providers[selectedProviderIndex]}:
            </Text>
          </Box>
          
          {loading && (
            <Text color="blue">Loading models...</Text>
          )}
          
          {error && (
            <Box marginBottom={1}>
              <Text color="red">
                ⚠️ {error}
              </Text>
            </Box>
          )}
          
          {!loading && models.length === 0 && (
            <Text color="gray">No models available</Text>
          )}
          
          {!loading && models.map((model, index) => (
            <Box key={model} marginBottom={1}>
              <Text color={
                index === selectedModelIndex 
                  ? (activeTab === 'models' ? 'cyan' : 'blue')
                  : 'gray'
              }>
                {index === selectedModelIndex ? '▶ ' : '  '}
                {model}
                {model === currentModel && providers[selectedProviderIndex] === currentProvider && ' (current)'}
              </Text>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Footer */}
      <Box marginTop={2} flexDirection="column">
        <Text color="gray">
          Use ↑↓ to navigate, Tab to switch tabs, Enter to select, Esc to close
        </Text>
        <Text color="gray" dimColor>
          Current: {currentModel} ({currentProvider})
        </Text>
      </Box>
    </Box>
  );
}
