import { OpenAI } from 'openai';
import { AppConfig, ResponseItem, ResponseInputItem, FunctionTool, ApprovalRequest } from '../../types/index.js';
import { createOpenAIClient, createStreamingCompletion } from '../openai-client.js';
import { inputItemsToMessages, streamResponses, createResponseItemFromEvents, createErrorItem } from '../responses.js';
import { handleExecCommand } from './handle-exec-command.js';
import { createInputItem } from '../input-utils.js';
import { processInputWithFiles } from '../multimodal.js';
import { trackMessage, trackError, trackPerformance } from '../analytics.js';
import { errorHandler } from '../error-handling.js';
import { PluginManager } from '../plugin-system.js';

export interface AgentLoopOptions {
  onProgress?: (partialResponse: string) => void;
  onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>;
  signal?: AbortSignal;
}

/**
 * Core agent loop that orchestrates AI interactions with tool calling
 */
export class AgentLoop {
  private model: string;
  private provider: string;
  private oai: OpenAI;
  private config: AppConfig;

  private pendingAborts: Set<string> = new Set();
  private pluginManager?: PluginManager;

  constructor(config: AppConfig) {
    this.config = config;
    this.model = config.model;
    this.provider = config.provider;
    this.oai = createOpenAIClient({
      provider: this.provider,
      timeout: config.timeout
    });

    // Initialize plugin manager
    this.initializePluginManager();
  }

  /**
   * Initialize plugin manager
   */
  private async initializePluginManager(): Promise<void> {
    try {
      const context = {
        config: this.config,
        workdir: this.config.workdir || process.cwd(),
        version: '1.0.0'
      };

      this.pluginManager = new PluginManager(context);
      await this.pluginManager.initialize();
    } catch (error) {
      console.warn('Failed to initialize plugin manager:', error);
    }
  }

  /**
   * Process a user message and return AI response
   */
  async processMessage(
    message: string,
    conversationHistory: ResponseItem[] = [],
    options: AgentLoopOptions = {}
  ): Promise<ResponseItem | null> {
    const { onProgress, onApprovalRequired, signal } = options;
    const startTime = Date.now();

    try {
      // Track message analytics
      trackMessage({
        model: this.model,
        provider: this.provider,
        messageLength: message.length
      });

      // Process message through plugins (beforeMessage hook)
      let processedMessage = message;
      if (this.pluginManager) {
        processedMessage = await this.pluginManager.executeHook('beforeMessage', message);
      }

      // Process multimodal input (files, images)
      const multimodalResult = await processInputWithFiles(processedMessage, this.config);
      if (multimodalResult.errors.length > 0) {
        console.warn('Multimodal processing errors:', multimodalResult.errors);
      }

      // Create input item from processed message
      const inputItem = await createInputItem(multimodalResult.text);

      // Build conversation context
      const context = this.buildConversationContext(conversationHistory, inputItem);

      // Convert to OpenAI messages
      const messages = inputItemsToMessages(context);
      
      // Add system message with instructions
      const systemMessage: OpenAI.ChatCompletionSystemMessageParam = {
        role: 'system',
        content: this.getSystemPrompt()
      };
      
      // Prepare tools
      const tools = this.getAvailableTools();
      
      // Create completion request
      const completionParams: OpenAI.ChatCompletionCreateParams = {
        model: this.model,
        messages: [systemMessage, ...messages],
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4096,
        stream: true
      };

      // Create streaming completion
      const completion = await createStreamingCompletion(this.oai, completionParams);
      
      // Process streaming response
      const events = [];
      let currentContent = '';
      
      for await (const event of streamResponses(completion)) {
        if (signal?.aborted) {
          throw new Error('Request aborted');
        }
        
        events.push(event);
        
        // Handle progress updates
        if (event.type === 'response.output_text.delta' && event.delta) {
          currentContent += event.delta;
          onProgress?.(currentContent);
        }
      }
      
      // Create response item from events
      const responseItem = createResponseItemFromEvents(events);
      
      // Handle function calls if present
      if (responseItem.content.some(c => c.type === 'function_call')) {
        const result = await this.handleFunctionCalls(responseItem, onApprovalRequired, signal);

        // Process response through plugins (afterMessage hook)
        if (this.pluginManager) {
          const processedResult = await this.pluginManager.executeHook('afterMessage', [result]);
          return Array.isArray(processedResult) && processedResult.length > 0 ? processedResult[0] : result;
        }

        return result;
      }

      // Track performance metrics
      const duration = Date.now() - startTime;
      trackPerformance({
        responseTime: duration,
        tokenCount: this.estimateTokenCount(responseItem),
        requestSize: message.length,
        responseSize: this.estimateResponseSize(responseItem),
        memoryUsage: process.memoryUsage().heapUsed
      });

      // Process response through plugins (afterMessage hook)
      if (this.pluginManager) {
        const processedResult = await this.pluginManager.executeHook('afterMessage', [responseItem]);
        return Array.isArray(processedResult) && processedResult.length > 0 ? processedResult[0] : responseItem;
      }

      return responseItem;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw error;
      }

      // Track error analytics
      trackError(error instanceof Error ? error : new Error(String(error)), {
        model: this.model,
        provider: this.provider,
        operation: 'processMessage'
      });

      // Process error through error handler
      const structuredError = errorHandler.processError(error, {
        operation: 'processMessage',
        component: 'AgentLoop',
        metadata: { model: this.model, provider: this.provider }
      });

      // Notify plugins of error
      if (this.pluginManager) {
        await this.pluginManager.executeHook('onError', error);
      }

      console.error('Agent loop error:', structuredError);
      return createErrorItem(structuredError.userMessage);
    }
  }

  /**
   * Handle function calls in AI response
   */
  private async handleFunctionCalls(
    responseItem: ResponseItem,
    onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>,
    signal?: AbortSignal
  ): Promise<ResponseItem> {
    const functionCalls = responseItem.content.filter(c => c.type === 'function_call');
    
    for (const content of functionCalls) {
      if (content.function_call) {
        try {
          const result = await this.executeFunctionCall(
            content.function_call,
            onApprovalRequired,
            signal
          );
          
          // Add function result to response
          responseItem.content.push({
            type: 'function_result',
            function_result: result
          });
        } catch (error) {
          // Add error result
          responseItem.content.push({
            type: 'function_result',
            function_result: {
              call_id: content.function_call.id,
              result: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
              success: false
            }
          });
        }
      }
    }
    
    return responseItem;
  }

  /**
   * Execute a function call
   */
  private async executeFunctionCall(
    functionCall: any,
    onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>,
    signal?: AbortSignal
  ): Promise<any> {
    const { name, arguments: argsString, id } = functionCall;
    
    try {
      const args = JSON.parse(argsString);
      
      if (name === 'shell' || name === 'execute_command') {
        const result = await handleExecCommand(
          args,
          this.config,
          this.config.approvalMode,
          this.config.additionalWritableRoots || [],
          onApprovalRequired,
          signal
        );
        
        return {
          call_id: id,
          result: result.outputText,
          success: true,
          metadata: result.metadata
        };
      }
      
      throw new Error(`Unknown function: ${name}`);
    } catch (error) {
      return {
        call_id: id,
        result: `Error executing function: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }

  /**
   * Build conversation context from history and current input
   */
  private buildConversationContext(
    history: ResponseItem[],
    currentInput: ResponseInputItem
  ): ResponseInputItem[] {
    // Convert history to input items
    const historyItems: ResponseInputItem[] = history.map(item => ({
      role: item.role,
      content: item.content.map(c => {
        switch (c.type) {
          case 'text':
            return { type: 'input_text' as const, text: c.text };
          case 'function_call':
            return { type: 'function_call' as const, function_call: c.function_call };
          case 'function_result':
            return { type: 'function_result' as const, function_result: c.function_result };
          default:
            return { type: 'input_text' as const, text: '' };
        }
      }),
      type: item.type,
      timestamp: item.timestamp
    }));
    
    return [...historyItems, currentInput];
  }

  /**
   * Get system prompt for the AI
   */
  private getSystemPrompt(): string {
    return `You are Kritrima AI, a sophisticated AI assistant with the ability to execute shell commands and interact with the file system. You are running in ${this.config.approvalMode} mode.

Key capabilities:
- Writing, debugging, and explaining code
- Execute shell commands using the shell function
- Read and write and modify files
- Analyze code and provide suggestions
- Help with development tasks

Guidelines:
- Be helpful and accurate in your responses
- When executing commands, explain what you're doing and why
- Be cautious with destructive operations
- Respect the user's approval mode settings
- Provide clear explanations of command outputs

Current working directory: ${this.config.workdir || process.cwd()}
Approval mode: ${this.config.approvalMode}

Available functions:
- shell(command: string[], workdir?: string, timeout?: number): Execute shell commands`;
  }

  /**
   * Get available tools for the AI
   */
  private getAvailableTools(): FunctionTool[] {
    const tools: FunctionTool[] = [];

    // Shell tool
    tools.push({
      type: 'function',
      function: {
        name: 'shell',
        description: 'Execute shell commands and return their output. Use this to run commands, read files, modify files, and interact with the system.',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'array',
              items: { type: 'string' },
              description: 'The command to execute as an array of strings (command and arguments)'
            },
            workdir: {
              type: 'string',
              description: 'Working directory for the command (optional, defaults to current directory)'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds (optional, defaults to 30000)'
            }
          },
          required: ['command']
        }
      }
    });

    return tools;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AppConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Track model switch for analytics
    if (newConfig.model && newConfig.model !== this.model) {
      const oldModel = this.model;
      this.model = newConfig.model;

      // Notify plugins of model switch
      if (this.pluginManager) {
        this.pluginManager.executeHook('onModelSwitch', { from: oldModel, to: this.model });
      }
    }

    // Update provider if changed
    if (newConfig.provider) this.provider = newConfig.provider;

    // Recreate OpenAI client if provider changed
    if (newConfig.provider || newConfig.timeout) {
      this.oai = createOpenAIClient({
        provider: this.provider,
        timeout: this.config.timeout
      });
    }

    // Update plugin manager context
    if (this.pluginManager) {
      this.pluginManager.updateContext({ config: this.config });
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    // History is managed externally now
  }

  /**
   * Abort pending operations
   */
  abort(): void {
    // Implementation would cancel ongoing requests
    this.pendingAborts.clear();
  }

  /**
   * Estimate token count for response
   */
  private estimateTokenCount(responseItem: ResponseItem): number {
    let tokenCount = 0;

    for (const content of responseItem.content) {
      if (content.type === 'text' && content.text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        tokenCount += Math.ceil(content.text.length / 4);
      }
    }

    return tokenCount;
  }

  /**
   * Estimate response size in bytes
   */
  private estimateResponseSize(responseItem: ResponseItem): number {
    let size = 0;

    for (const content of responseItem.content) {
      if (content.type === 'text' && content.text) {
        size += Buffer.byteLength(content.text, 'utf8');
      }
    }

    return size;
  }

  /**
   * Shutdown agent loop and cleanup resources
   */
  async shutdown(): Promise<void> {
    if (this.pluginManager) {
      await this.pluginManager.shutdown();
    }

    this.pendingAborts.clear();
  }
}
