import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
export function SessionsOverlay({ onSessionLoad, onSessionSave, onSessionDelete, onClose }) {
    const [activeTab, setActiveTab] = useState('load');
    const [sessions, setSessions] = useState([]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [saveSessionName, setSaveSessionName] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // Load sessions on mount
    useEffect(() => {
        loadSessions();
    }, []);
    const loadSessions = async () => {
        setLoading(true);
        setError(null);
        try {
            const loadedSessions = await loadSessionsFromStorage();
            setSessions(loadedSessions);
            setSelectedIndex(0);
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load sessions');
        }
        finally {
            setLoading(false);
        }
    };
    useInput((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.tab) {
            const tabs = ['load', 'save', 'manage'];
            const currentIndex = tabs.indexOf(activeTab);
            const nextIndex = (currentIndex + 1) % tabs.length;
            setActiveTab(tabs[nextIndex]);
            return;
        }
        if (activeTab === 'save') {
            if (key.return) {
                if (saveSessionName.trim()) {
                    onSessionSave(saveSessionName.trim());
                    onClose();
                }
                return;
            }
            if (key.backspace) {
                setSaveSessionName(prev => prev.slice(0, -1));
                return;
            }
            if (input && !key.ctrl && !key.meta) {
                setSaveSessionName(prev => prev + input);
                return;
            }
        }
        else {
            if (key.return) {
                if (activeTab === 'load' && sessions[selectedIndex]) {
                    onSessionLoad(sessions[selectedIndex]);
                    onClose();
                }
                else if (activeTab === 'manage' && sessions[selectedIndex]) {
                    // Delete session
                    onSessionDelete(sessions[selectedIndex].id);
                    loadSessions();
                }
                return;
            }
            if (key.upArrow) {
                setSelectedIndex(prev => prev > 0 ? prev - 1 : sessions.length - 1);
                return;
            }
            if (key.downArrow) {
                setSelectedIndex(prev => prev < sessions.length - 1 ? prev + 1 : 0);
                return;
            }
        }
    });
    const formatDate = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };
    const formatDuration = (created, updated) => {
        const duration = updated - created;
        const minutes = Math.floor(duration / 60000);
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        }
        else {
            return `${minutes}m`;
        }
    };
    return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDCBE Session Management" }) }), _jsxs(Box, { marginBottom: 2, children: [_jsx(Box, { marginRight: 4, children: _jsxs(Text, { color: activeTab === 'load' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'load' ? '▶ ' : '  ', "Load"] }) }), _jsx(Box, { marginRight: 4, children: _jsxs(Text, { color: activeTab === 'save' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'save' ? '▶ ' : '  ', "Save"] }) }), _jsx(Box, { children: _jsxs(Text, { color: activeTab === 'manage' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'manage' ? '▶ ' : '  ', "Manage"] }) })] }), _jsxs(Box, { flexDirection: "column", flexGrow: 1, children: [loading && (_jsx(Text, { color: "blue", children: "Loading sessions..." })), error && (_jsxs(Text, { color: "red", children: ["\u274C ", error] })), !loading && !error && activeTab === 'save' && (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Save Current Session:" }) }), _jsxs(Box, { marginBottom: 2, children: [_jsx(Text, { color: "green", children: "Name: " }), _jsx(Text, { children: saveSessionName }), _jsx(Text, { color: "gray", children: "_" })] }), _jsx(Text, { color: "gray", children: "Type a name for your session and press Enter to save." })] })), !loading && !error && (activeTab === 'load' || activeTab === 'manage') && (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: activeTab === 'load' ? 'Available Sessions:' : 'Manage Sessions:' }) }), sessions.length === 0 ? (_jsx(Text, { color: "gray", children: "No saved sessions found." })) : (sessions.map((session, index) => (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Box, { children: _jsxs(Text, { color: index === selectedIndex
                                                ? (activeTab === 'load' ? 'cyan' : activeTab === 'manage' ? 'red' : 'blue')
                                                : 'gray', children: [index === selectedIndex ? '▶ ' : '  ', session.name, activeTab === 'manage' && index === selectedIndex && ' [DELETE]'] }) }), _jsx(Box, { paddingLeft: 3, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Created: ", formatDate(session.created), " | Updated: ", formatDate(session.updated), " | Duration: ", formatDuration(session.created, session.updated), " | Messages: ", session.items.length] }) }), session.config.model && (_jsx(Box, { paddingLeft: 3, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Model: ", session.config.model, " (", session.config.provider, ")"] }) }))] }, session.id))))] }))] }), _jsxs(Box, { marginTop: 2, flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "Use \u2191\u2193 to navigate, Tab to switch tabs, Enter to select, Esc to close" }), activeTab === 'save' && (_jsx(Text, { color: "gray", dimColor: true, children: "Type session name and press Enter to save" })), activeTab === 'manage' && (_jsx(Text, { color: "red", dimColor: true, children: "Press Enter to DELETE selected session (cannot be undone)" }))] })] }));
}
/**
 * Load sessions from storage
 */
async function loadSessionsFromStorage() {
    try {
        const fs = require('fs-extra');
        const path = require('path');
        const os = require('os');
        const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
        if (!fs.existsSync(sessionsDir)) {
            return [];
        }
        const files = fs.readdirSync(sessionsDir);
        const sessions = [];
        for (const file of files) {
            if (file.endsWith('.json')) {
                try {
                    const filePath = path.join(sessionsDir, file);
                    const sessionData = fs.readJsonSync(filePath);
                    sessions.push(sessionData);
                }
                catch (error) {
                    console.warn(`Failed to load session ${file}:`, error);
                }
            }
        }
        // Sort by updated time (most recent first)
        return sessions.sort((a, b) => b.updated - a.updated);
    }
    catch (error) {
        throw new Error(`Failed to load sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Save session to storage
 */
export async function saveSessionToStorage(session) {
    try {
        const fs = require('fs-extra');
        const path = require('path');
        const os = require('os');
        const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
        await fs.ensureDir(sessionsDir);
        const fileName = `${session.id}.json`;
        const filePath = path.join(sessionsDir, fileName);
        await fs.writeJson(filePath, session, { spaces: 2 });
    }
    catch (error) {
        throw new Error(`Failed to save session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Delete session from storage
 */
export async function deleteSessionFromStorage(sessionId) {
    try {
        const fs = require('fs-extra');
        const path = require('path');
        const os = require('os');
        const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
        const fileName = `${sessionId}.json`;
        const filePath = path.join(sessionsDir, fileName);
        if (fs.existsSync(filePath)) {
            await fs.remove(filePath);
        }
    }
    catch (error) {
        throw new Error(`Failed to delete session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
//# sourceMappingURL=sessions-overlay.js.map