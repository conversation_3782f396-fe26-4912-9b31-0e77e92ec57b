import * as fs from 'fs-extra';
import { promises as fsPromises } from 'fs';
import * as path from 'path';
import * as os from 'os';
import { v4 as uuidv4 } from 'uuid';
/**
 * Session storage directory
 */
const SESSIONS_DIR = path.join(os.homedir(), '.kritrima-ai', 'sessions');
/**
 * Session manager class
 */
export class SessionManager {
    sessionsDir;
    constructor(customDir) {
        this.sessionsDir = customDir || SESSIONS_DIR;
    }
    /**
     * Initialize session storage
     */
    async initialize() {
        await fs.ensureDir(this.sessionsDir);
    }
    /**
     * Create a new session
     */
    async createSession(name, config, initialItems = []) {
        await this.initialize();
        const session = {
            id: uuidv4(),
            name: name.trim(),
            created: Date.now(),
            updated: Date.now(),
            items: initialItems,
            config: {
                model: config.model || 'gpt-4',
                provider: config.provider || 'openai',
                approvalMode: config.approvalMode,
                maxTokens: config.maxTokens,
                temperature: config.temperature
            }
        };
        await this.saveSession(session);
        return session;
    }
    /**
     * Save session to storage
     */
    async saveSession(session) {
        await this.initialize();
        session.updated = Date.now();
        const filePath = path.join(this.sessionsDir, `${session.id}.json`);
        await fs.outputFile(filePath, JSON.stringify(session, null, 2), 'utf-8');
    }
    /**
     * Load session from storage
     */
    async loadSession(sessionId) {
        try {
            const filePath = path.join(this.sessionsDir, `${sessionId}.json`);
            if (!await fs.pathExists(filePath)) {
                return null;
            }
            const sessionData = await fsPromises.readFile(filePath, 'utf-8');
            const session = JSON.parse(sessionData);
            // Validate session structure
            if (!this.isValidSession(session)) {
                console.warn(`Invalid session structure: ${sessionId}`);
                return null;
            }
            return session;
        }
        catch (error) {
            console.error(`Failed to load session ${sessionId}:`, error);
            return null;
        }
    }
    /**
     * Delete session from storage
     */
    async deleteSession(sessionId) {
        try {
            const filePath = path.join(this.sessionsDir, `${sessionId}.json`);
            if (await fs.pathExists(filePath)) {
                await fs.remove(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete session ${sessionId}:`, error);
            return false;
        }
    }
    /**
     * List all sessions with metadata
     */
    async listSessions() {
        try {
            await this.initialize();
            const files = await fsPromises.readdir(this.sessionsDir);
            const sessionFiles = files.filter(file => file.endsWith('.json'));
            const sessions = [];
            for (const file of sessionFiles) {
                try {
                    const filePath = path.join(this.sessionsDir, file);
                    const stats = await fsPromises.stat(filePath);
                    const sessionData = await fsPromises.readFile(filePath, 'utf-8');
                    const session = JSON.parse(sessionData);
                    if (this.isValidSession(session)) {
                        sessions.push({
                            id: session.id,
                            name: session.name,
                            created: session.created,
                            updated: session.updated,
                            messageCount: session.items.length,
                            model: session.config.model || 'unknown',
                            provider: session.config.provider || 'unknown',
                            size: stats.size
                        });
                    }
                }
                catch (error) {
                    console.warn(`Failed to read session file ${file}:`, error);
                }
            }
            // Sort by updated time (most recent first)
            return sessions.sort((a, b) => b.updated - a.updated);
        }
        catch (error) {
            console.error('Failed to list sessions:', error);
            return [];
        }
    }
    /**
     * Search sessions
     */
    async searchSessions(options = {}) {
        const allSessions = await this.listSessions();
        let filteredSessions = allSessions;
        // Apply filters
        if (options.query) {
            const query = options.query.toLowerCase();
            filteredSessions = filteredSessions.filter(session => session.name.toLowerCase().includes(query));
        }
        if (options.model) {
            filteredSessions = filteredSessions.filter(session => session.model === options.model);
        }
        if (options.provider) {
            filteredSessions = filteredSessions.filter(session => session.provider === options.provider);
        }
        if (options.dateFrom) {
            filteredSessions = filteredSessions.filter(session => session.created >= options.dateFrom.getTime());
        }
        if (options.dateTo) {
            filteredSessions = filteredSessions.filter(session => session.created <= options.dateTo.getTime());
        }
        // Apply sorting
        if (options.sortBy) {
            const sortBy = options.sortBy;
            const sortOrder = options.sortOrder || 'desc';
            filteredSessions.sort((a, b) => {
                let aValue = a[sortBy];
                let bValue = b[sortBy];
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
                if (sortOrder === 'asc') {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                }
                else {
                    return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                }
            });
        }
        // Apply limit
        if (options.limit) {
            filteredSessions = filteredSessions.slice(0, options.limit);
        }
        return filteredSessions;
    }
    /**
     * Update session items
     */
    async updateSessionItems(sessionId, items) {
        const session = await this.loadSession(sessionId);
        if (!session) {
            return false;
        }
        session.items = items;
        await this.saveSession(session);
        return true;
    }
    /**
     * Add items to session
     */
    async addItemsToSession(sessionId, newItems) {
        const session = await this.loadSession(sessionId);
        if (!session) {
            return false;
        }
        session.items.push(...newItems);
        await this.saveSession(session);
        return true;
    }
    /**
     * Export session to different formats
     */
    async exportSession(sessionId, options) {
        const session = await this.loadSession(sessionId);
        if (!session) {
            return null;
        }
        let content;
        switch (options.format) {
            case 'json':
                content = JSON.stringify(session, null, 2);
                break;
            case 'markdown':
                content = this.formatSessionAsMarkdown(session, options);
                break;
            case 'txt':
                content = this.formatSessionAsText(session, options);
                break;
            default:
                throw new Error(`Unsupported export format: ${options.format}`);
        }
        if (options.outputPath) {
            await fs.outputFile(options.outputPath, content, 'utf-8');
        }
        return content;
    }
    /**
     * Get session statistics
     */
    async getSessionStats() {
        const sessions = await this.listSessions();
        const stats = {
            totalSessions: sessions.length,
            totalMessages: sessions.reduce((sum, s) => sum + s.messageCount, 0),
            totalSize: sessions.reduce((sum, s) => sum + s.size, 0),
            oldestSession: sessions.length > 0 ? Math.min(...sessions.map(s => s.created)) : 0,
            newestSession: sessions.length > 0 ? Math.max(...sessions.map(s => s.updated)) : 0,
            providerBreakdown: {},
            modelBreakdown: {}
        };
        // Calculate breakdowns
        sessions.forEach(session => {
            stats.providerBreakdown[session.provider] =
                (stats.providerBreakdown[session.provider] || 0) + 1;
            stats.modelBreakdown[session.model] =
                (stats.modelBreakdown[session.model] || 0) + 1;
        });
        return stats;
    }
    /**
     * Clean up old sessions
     */
    async cleanupOldSessions(olderThanDays) {
        const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
        const sessions = await this.listSessions();
        let deletedCount = 0;
        for (const session of sessions) {
            if (session.updated < cutoffTime) {
                const deleted = await this.deleteSession(session.id);
                if (deleted) {
                    deletedCount++;
                }
            }
        }
        return deletedCount;
    }
    /**
     * Validate session structure
     */
    isValidSession(obj) {
        return obj &&
            typeof obj.id === 'string' &&
            typeof obj.name === 'string' &&
            typeof obj.created === 'number' &&
            typeof obj.updated === 'number' &&
            Array.isArray(obj.items) &&
            obj.config &&
            typeof obj.config.model === 'string' &&
            typeof obj.config.provider === 'string';
    }
    /**
     * Format session as markdown
     */
    formatSessionAsMarkdown(session, options) {
        let content = `# ${session.name}\n\n`;
        if (options.includeMetadata) {
            content += `**Created:** ${new Date(session.created).toLocaleString()}\n`;
            content += `**Updated:** ${new Date(session.updated).toLocaleString()}\n`;
            content += `**Model:** ${session.config.model}\n`;
            content += `**Provider:** ${session.config.provider}\n`;
            content += `**Messages:** ${session.items.length}\n\n`;
            content += '---\n\n';
        }
        session.items.forEach((item, index) => {
            if (!options.includeSystemMessages && item.role === 'system') {
                return;
            }
            content += `## Message ${index + 1} (${item.role})\n\n`;
            if (Array.isArray(item.content)) {
                item.content.forEach(contentItem => {
                    if (contentItem.type === 'text') {
                        content += `${contentItem.text}\n\n`;
                    }
                });
            }
            else {
                content += `${item.content}\n\n`;
            }
        });
        return content;
    }
    /**
     * Format session as plain text
     */
    formatSessionAsText(session, options) {
        let content = `${session.name}\n${'='.repeat(session.name.length)}\n\n`;
        if (options.includeMetadata) {
            content += `Created: ${new Date(session.created).toLocaleString()}\n`;
            content += `Updated: ${new Date(session.updated).toLocaleString()}\n`;
            content += `Model: ${session.config.model}\n`;
            content += `Provider: ${session.config.provider}\n`;
            content += `Messages: ${session.items.length}\n\n`;
            content += '-'.repeat(50) + '\n\n';
        }
        session.items.forEach((item, index) => {
            if (!options.includeSystemMessages && item.role === 'system') {
                return;
            }
            content += `[${item.role.toUpperCase()}] Message ${index + 1}:\n`;
            if (Array.isArray(item.content)) {
                item.content.forEach(contentItem => {
                    if (contentItem.type === 'text') {
                        content += `${contentItem.text}\n`;
                    }
                });
            }
            else {
                content += `${item.content}\n`;
            }
            content += '\n';
        });
        return content;
    }
}
/**
 * Default session manager instance
 */
export const sessionManager = new SessionManager();
//# sourceMappingURL=session-manager.js.map