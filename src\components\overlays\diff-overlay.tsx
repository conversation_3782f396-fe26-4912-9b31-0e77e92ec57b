import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';

interface DiffOverlayProps {
  filePath: string;
  oldContent: string;
  newContent: string;
  onApprove: () => void;
  onReject: () => void;
  onClose: () => void;
}

interface DiffLine {
  type: 'context' | 'added' | 'removed';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

export function DiffOverlay({ filePath, oldContent, newContent, onApprove, onReject, onClose }: DiffOverlayProps) {
  const [diffLines, setDiffLines] = useState<DiffLine[]>([]);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'reject'>('approve');
  const [viewHeight] = useState(20); // Visible lines in diff view

  useEffect(() => {
    const diff = generateDiff(oldContent, newContent);
    setDiffLines(diff);
  }, [oldContent, newContent]);

  useInput((input, key) => {
    if (key.escape) {
      onClose();
      return;
    }

    if (key.return) {
      if (selectedAction === 'approve') {
        onApprove();
      } else {
        onReject();
      }
      return;
    }

    if (key.tab) {
      setSelectedAction(prev => prev === 'approve' ? 'reject' : 'approve');
      return;
    }

    if (key.upArrow) {
      setScrollPosition(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.downArrow) {
      setScrollPosition(prev => Math.min(diffLines.length - viewHeight, prev + 1));
      return;
    }

    if (key.pageUp) {
      setScrollPosition(prev => Math.max(0, prev - viewHeight));
      return;
    }

    if (key.pageDown) {
      setScrollPosition(prev => Math.min(diffLines.length - viewHeight, prev + viewHeight));
      return;
    }

    if (input === 'a' || input === 'A') {
      onApprove();
      return;
    }

    if (input === 'r' || input === 'R') {
      onReject();
      return;
    }
  });

  const visibleLines = diffLines.slice(scrollPosition, scrollPosition + viewHeight);
  const hasMoreAbove = scrollPosition > 0;
  const hasMoreBelow = scrollPosition + viewHeight < diffLines.length;

  const getLineColor = (line: DiffLine) => {
    switch (line.type) {
      case 'added':
        return 'green';
      case 'removed':
        return 'red';
      case 'context':
      default:
        return 'gray';
    }
  };

  const getLinePrefix = (line: DiffLine) => {
    switch (line.type) {
      case 'added':
        return '+';
      case 'removed':
        return '-';
      case 'context':
      default:
        return ' ';
    }
  };

  const formatLineNumbers = (line: DiffLine) => {
    const oldNum = line.oldLineNumber ? line.oldLineNumber.toString().padStart(4) : '    ';
    const newNum = line.newLineNumber ? line.newLineNumber.toString().padStart(4) : '    ';
    return `${oldNum} ${newNum}`;
  };

  return (
    <Box flexDirection="column" height="100%" padding={1}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color="cyan" bold>
          📝 File Diff: {filePath}
        </Text>
      </Box>

      {/* Stats */}
      <Box marginBottom={1}>
        <Text color="gray">
          Lines: {diffLines.filter(l => l.type === 'added').length} added, {' '}
          {diffLines.filter(l => l.type === 'removed').length} removed, {' '}
          {diffLines.filter(l => l.type === 'context').length} unchanged
        </Text>
      </Box>

      {/* Scroll indicators */}
      {hasMoreAbove && (
        <Box marginBottom={1}>
          <Text color="blue">▲ More lines above (scroll up to see)</Text>
        </Box>
      )}

      {/* Diff content */}
      <Box flexDirection="column" flexGrow={1} borderStyle="round" paddingX={1}>
        {visibleLines.map((line, index) => (
          <Box key={scrollPosition + index}>
            <Text color="gray" dimColor>
              {formatLineNumbers(line)}
            </Text>
            <Text color={getLineColor(line)}>
              {getLinePrefix(line)} {line.content}
            </Text>
          </Box>
        ))}
        
        {diffLines.length === 0 && (
          <Text color="gray">No differences found</Text>
        )}
      </Box>

      {/* Scroll indicators */}
      {hasMoreBelow && (
        <Box marginTop={1}>
          <Text color="blue">▼ More lines below (scroll down to see)</Text>
        </Box>
      )}

      {/* Action buttons */}
      <Box marginTop={2} justifyContent="center">
        <Box marginRight={4}>
          <Text color={selectedAction === 'approve' ? 'green' : 'gray'} bold>
            {selectedAction === 'approve' ? '▶ ' : '  '}[A]pprove
          </Text>
        </Box>
        <Box>
          <Text color={selectedAction === 'reject' ? 'red' : 'gray'} bold>
            {selectedAction === 'reject' ? '▶ ' : '  '}[R]eject
          </Text>
        </Box>
      </Box>

      {/* Footer */}
      <Box marginTop={1} flexDirection="column">
        <Text color="gray">
          Use ↑↓ to scroll, PgUp/PgDn for page scroll, Tab to switch action
        </Text>
        <Text color="gray" dimColor>
          Press A to approve, R to reject, Enter to confirm, Esc to close
        </Text>
      </Box>
    </Box>
  );
}

/**
 * Generate diff lines from old and new content
 */
function generateDiff(oldContent: string, newContent: string): DiffLine[] {
  const oldLines = oldContent.split('\n');
  const newLines = newContent.split('\n');
  const diffLines: DiffLine[] = [];

  // Simple line-by-line diff algorithm
  // In a production implementation, you'd use a more sophisticated algorithm like Myers
  let oldIndex = 0;
  let newIndex = 0;

  while (oldIndex < oldLines.length || newIndex < newLines.length) {
    const oldLine = oldLines[oldIndex];
    const newLine = newLines[newIndex];

    if (oldIndex >= oldLines.length) {
      // Only new lines remaining
      diffLines.push({
        type: 'added',
        content: newLine,
        newLineNumber: newIndex + 1
      });
      newIndex++;
    } else if (newIndex >= newLines.length) {
      // Only old lines remaining
      diffLines.push({
        type: 'removed',
        content: oldLine,
        oldLineNumber: oldIndex + 1
      });
      oldIndex++;
    } else if (oldLine === newLine) {
      // Lines are the same
      diffLines.push({
        type: 'context',
        content: oldLine,
        oldLineNumber: oldIndex + 1,
        newLineNumber: newIndex + 1
      });
      oldIndex++;
      newIndex++;
    } else {
      // Lines are different - look ahead to see if we can find a match
      let foundMatch = false;
      const lookAhead = 5;

      // Look for the new line in upcoming old lines
      for (let i = 1; i <= lookAhead && oldIndex + i < oldLines.length; i++) {
        if (oldLines[oldIndex + i] === newLine) {
          // Found match - mark intermediate old lines as removed
          for (let j = 0; j < i; j++) {
            diffLines.push({
              type: 'removed',
              content: oldLines[oldIndex + j],
              oldLineNumber: oldIndex + j + 1
            });
          }
          oldIndex += i;
          foundMatch = true;
          break;
        }
      }

      if (!foundMatch) {
        // Look for the old line in upcoming new lines
        for (let i = 1; i <= lookAhead && newIndex + i < newLines.length; i++) {
          if (newLines[newIndex + i] === oldLine) {
            // Found match - mark intermediate new lines as added
            for (let j = 0; j < i; j++) {
              diffLines.push({
                type: 'added',
                content: newLines[newIndex + j],
                newLineNumber: newIndex + j + 1
              });
            }
            newIndex += i;
            foundMatch = true;
            break;
          }
        }
      }

      if (!foundMatch) {
        // No match found - treat as removed and added
        diffLines.push({
          type: 'removed',
          content: oldLine,
          oldLineNumber: oldIndex + 1
        });
        diffLines.push({
          type: 'added',
          content: newLine,
          newLineNumber: newIndex + 1
        });
        oldIndex++;
        newIndex++;
      }
    }
  }

  return diffLines;
}

/**
 * Calculate diff statistics
 */
export function calculateDiffStats(oldContent: string, newContent: string): {
  linesAdded: number;
  linesRemoved: number;
  linesChanged: number;
} {
  const diff = generateDiff(oldContent, newContent);
  
  return {
    linesAdded: diff.filter(line => line.type === 'added').length,
    linesRemoved: diff.filter(line => line.type === 'removed').length,
    linesChanged: diff.filter(line => line.type === 'context').length
  };
}
