import * as fs from 'fs-extra';
import * as path from 'path';
import { glob } from 'glob';
/**
 * Advanced file operations manager
 */
export class FileOperationsManager {
    config;
    allowedPaths;
    constructor(config) {
        this.config = config;
        this.allowedPaths = [
            path.resolve(config.workdir || process.cwd()),
            ...(config.additionalWritableRoots || []).map(root => path.resolve(root))
        ];
    }
    /**
     * Validate file path is within allowed directories
     */
    validatePath(filePath) {
        const resolvedPath = path.resolve(filePath);
        return this.allowedPaths.some(allowedPath => resolvedPath.startsWith(allowedPath));
    }
    /**
     * Get file metadata
     */
    async getFileMetadata(filePath) {
        try {
            if (!this.validatePath(filePath)) {
                throw new Error('File path not allowed');
            }
            const stats = await fs.stat(filePath);
            const parsedPath = path.parse(filePath);
            // Check permissions
            const permissions = {
                readable: false,
                writable: false,
                executable: false
            };
            try {
                await fs.access(filePath, fs.constants.R_OK);
                permissions.readable = true;
            }
            catch { }
            try {
                await fs.access(filePath, fs.constants.W_OK);
                permissions.writable = true;
            }
            catch { }
            try {
                await fs.access(filePath, fs.constants.X_OK);
                permissions.executable = true;
            }
            catch { }
            return {
                path: filePath,
                name: parsedPath.name,
                extension: parsedPath.ext,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                isDirectory: stats.isDirectory(),
                isFile: stats.isFile(),
                permissions
            };
        }
        catch (error) {
            console.error(`Failed to get metadata for ${filePath}:`, error);
            return null;
        }
    }
    /**
     * Search files with advanced options
     */
    async searchFiles(searchOptions = {}) {
        const { pattern = '**/*', extensions = [], includeHidden = false, maxDepth = 10, maxResults = 1000, caseSensitive = false, regex = false, content, modifiedAfter, modifiedBefore, sizeMin, sizeMax } = searchOptions;
        const results = [];
        try {
            for (const allowedPath of this.allowedPaths) {
                const searchPattern = path.join(allowedPath, pattern);
                const globOptions = {
                    dot: includeHidden,
                    maxDepth,
                    nocase: !caseSensitive,
                    absolute: true
                };
                const files = await glob(searchPattern, globOptions);
                for (const file of files) {
                    if (results.length >= maxResults)
                        break;
                    const metadata = await this.getFileMetadata(file);
                    if (!metadata || !metadata.isFile)
                        continue;
                    // Filter by extensions
                    if (extensions.length > 0 && !extensions.includes(metadata.extension)) {
                        continue;
                    }
                    // Filter by modification date
                    if (modifiedAfter && metadata.modified < modifiedAfter)
                        continue;
                    if (modifiedBefore && metadata.modified > modifiedBefore)
                        continue;
                    // Filter by size
                    if (sizeMin !== undefined && metadata.size < sizeMin)
                        continue;
                    if (sizeMax !== undefined && metadata.size > sizeMax)
                        continue;
                    // Filter by content
                    if (content) {
                        try {
                            const fileContent = await fs.readFile(file, 'utf-8');
                            const searchRegex = regex ? new RegExp(content, caseSensitive ? 'g' : 'gi') : null;
                            const matches = searchRegex ?
                                searchRegex.test(fileContent) :
                                caseSensitive ?
                                    fileContent.includes(content) :
                                    fileContent.toLowerCase().includes(content.toLowerCase());
                            if (!matches)
                                continue;
                        }
                        catch {
                            // Skip files that can't be read as text
                            continue;
                        }
                    }
                    results.push(metadata);
                }
            }
        }
        catch (error) {
            console.error('File search failed:', error);
        }
        return results;
    }
    /**
     * Analyze file content
     */
    async analyzeFile(filePath) {
        try {
            if (!this.validatePath(filePath)) {
                throw new Error('File path not allowed');
            }
            const content = await fs.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            const words = content.split(/\s+/).filter(word => word.length > 0);
            const analysis = {
                path: filePath,
                language: this.detectLanguage(filePath, content),
                lineCount: lines.length,
                characterCount: content.length,
                wordCount: words.length,
                dependencies: [],
                exports: [],
                imports: [],
                functions: [],
                classes: [],
                comments: 0
            };
            // Language-specific analysis
            if (analysis.language) {
                await this.performLanguageSpecificAnalysis(content, analysis);
            }
            return analysis;
        }
        catch (error) {
            console.error(`Failed to analyze file ${filePath}:`, error);
            return null;
        }
    }
    /**
     * Detect programming language
     */
    detectLanguage(filePath, content) {
        const extension = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.py': 'python',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'bash',
            '.ps1': 'powershell',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.less': 'less',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.dockerfile': 'dockerfile'
        };
        return languageMap[extension];
    }
    /**
     * Perform language-specific analysis
     */
    async performLanguageSpecificAnalysis(content, analysis) {
        const lines = content.split('\n');
        switch (analysis.language) {
            case 'javascript':
            case 'typescript':
                this.analyzeJavaScript(content, lines, analysis);
                break;
            case 'python':
                this.analyzePython(content, lines, analysis);
                break;
            case 'java':
                this.analyzeJava(content, lines, analysis);
                break;
            default:
                this.analyzeGeneric(content, lines, analysis);
        }
    }
    /**
     * Analyze JavaScript/TypeScript files
     */
    analyzeJavaScript(content, lines, analysis) {
        // Find imports
        const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            analysis.imports.push(match[1]);
        }
        // Find requires
        const requireRegex = /require\(['"`]([^'"`]+)['"`]\)/g;
        while ((match = requireRegex.exec(content)) !== null) {
            analysis.dependencies.push(match[1]);
        }
        // Find functions
        const functionRegex = /(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:function|\([^)]*\)\s*=>))/g;
        while ((match = functionRegex.exec(content)) !== null) {
            analysis.functions.push(match[1] || match[2]);
        }
        // Find classes
        const classRegex = /class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            analysis.classes.push(match[1]);
        }
        // Count comments
        analysis.comments = (content.match(/\/\/.*|\/\*[\s\S]*?\*\//g) || []).length;
    }
    /**
     * Analyze Python files
     */
    analyzePython(content, lines, analysis) {
        // Find imports
        const importRegex = /(?:from\s+(\S+)\s+import|import\s+(\S+))/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            analysis.imports.push(match[1] || match[2]);
        }
        // Find functions
        const functionRegex = /def\s+(\w+)/g;
        while ((match = functionRegex.exec(content)) !== null) {
            analysis.functions.push(match[1]);
        }
        // Find classes
        const classRegex = /class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            analysis.classes.push(match[1]);
        }
        // Count comments
        analysis.comments = lines.filter(line => line.trim().startsWith('#')).length;
    }
    /**
     * Analyze Java files
     */
    analyzeJava(content, lines, analysis) {
        // Find imports
        const importRegex = /import\s+([^;]+);/g;
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            analysis.imports.push(match[1]);
        }
        // Find methods
        const methodRegex = /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(/g;
        while ((match = methodRegex.exec(content)) !== null) {
            analysis.functions.push(match[1]);
        }
        // Find classes
        const classRegex = /(?:public\s+)?class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            analysis.classes.push(match[1]);
        }
        // Count comments
        analysis.comments = (content.match(/\/\/.*|\/\*[\s\S]*?\*\//g) || []).length;
    }
    /**
     * Generic analysis for unknown languages
     */
    analyzeGeneric(content, lines, analysis) {
        // Count different types of comments
        const singleLineComments = (content.match(/\/\/.*|#.*/g) || []).length;
        const multiLineComments = (content.match(/\/\*[\s\S]*?\*\/|'''[\s\S]*?'''|"""[\s\S]*?"""/g) || []).length;
        analysis.comments = singleLineComments + multiLineComments;
    }
    /**
     * Get project structure
     */
    async getProjectStructure() {
        const structure = {
            root: this.config.workdir || process.cwd(),
            files: [],
            directories: [],
            languages: {},
            totalFiles: 0,
            totalSize: 0,
            structure: {}
        };
        try {
            const files = await this.searchFiles({ maxResults: 10000 });
            structure.files = files;
            structure.totalFiles = files.length;
            structure.totalSize = files.reduce((sum, file) => sum + file.size, 0);
            // Count languages
            for (const file of files) {
                const language = this.detectLanguage(file.path, '');
                if (language) {
                    structure.languages[language] = (structure.languages[language] || 0) + 1;
                }
            }
            // Build directory structure
            const directories = new Set();
            for (const file of files) {
                let dir = path.dirname(file.path);
                while (dir !== structure.root && dir !== path.dirname(dir)) {
                    directories.add(dir);
                    dir = path.dirname(dir);
                }
            }
            structure.directories = Array.from(directories).sort();
        }
        catch (error) {
            console.error('Failed to get project structure:', error);
        }
        return structure;
    }
    /**
     * Create file backup
     */
    async createBackup(filePath) {
        try {
            if (!this.validatePath(filePath)) {
                throw new Error('File path not allowed');
            }
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = `${filePath}.backup.${timestamp}`;
            await fs.copy(filePath, backupPath);
            return backupPath;
        }
        catch (error) {
            console.error(`Failed to create backup for ${filePath}:`, error);
            return null;
        }
    }
    /**
     * Restore file from backup
     */
    async restoreFromBackup(originalPath, backupPath) {
        try {
            if (!this.validatePath(originalPath) || !this.validatePath(backupPath)) {
                throw new Error('File path not allowed');
            }
            await fs.copy(backupPath, originalPath);
            return true;
        }
        catch (error) {
            console.error(`Failed to restore ${originalPath} from backup:`, error);
            return false;
        }
    }
    /**
     * Clean up old backups
     */
    async cleanupBackups(olderThanDays = 7) {
        let cleanedCount = 0;
        const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
        try {
            const backupFiles = await this.searchFiles({
                pattern: '**/*.backup.*',
                maxResults: 10000
            });
            for (const backup of backupFiles) {
                if (backup.modified.getTime() < cutoffTime) {
                    try {
                        await fs.remove(backup.path);
                        cleanedCount++;
                    }
                    catch (error) {
                        console.warn(`Failed to remove backup ${backup.path}:`, error);
                    }
                }
            }
        }
        catch (error) {
            console.error('Failed to cleanup backups:', error);
        }
        return cleanedCount;
    }
}
//# sourceMappingURL=file-operations.js.map