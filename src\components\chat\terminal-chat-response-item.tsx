
import { Box, Text } from 'ink';
import { ResponseItem } from '../../types';

interface TerminalChatResponseItemProps {
  item: ResponseItem;
}

export function TerminalChatResponseItem({ item }: TerminalChatResponseItemProps) {
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const renderContent = () => {
    return item.content.map((content, index) => {
      switch (content.type) {
        case 'text':
          return (
            <Box key={index} flexDirection="column">
              <Text>{content.text}</Text>
            </Box>
          );
        
        case 'function_call':
          if (content.function_call) {
            return (
              <Box key={index} flexDirection="column" marginY={1} paddingX={2} borderStyle="round">
                <Text color="blue" bold>
                  🔧 Function Call: {content.function_call.name}
                </Text>
                <Text color="gray">
                  Arguments: {content.function_call.arguments}
                </Text>
              </Box>
            );
          }
          break;
        
        case 'function_result':
          if (content.function_result) {
            return (
              <Box key={index} flexDirection="column" marginY={1} paddingX={2} borderStyle="round">
                <Text color={content.function_result.success ? "green" : "red"} bold>
                  {content.function_result.success ? "✅" : "❌"} Function Result
                </Text>
                <Text>{content.function_result.result}</Text>
                {content.function_result.metadata && (
                  <Text color="gray" dimColor>
                    Metadata: {JSON.stringify(content.function_result.metadata, null, 2)}
                  </Text>
                )}
              </Box>
            );
          }
          break;
        
        default:
          return null;
      }
    });
  };

  const getRoleIcon = () => {
    switch (item.role) {
      case 'user':
        return '👤';
      case 'assistant':
        return '🤖';
      case 'system':
        return '⚙️';
      case 'tool':
        return '🔧';
      default:
        return '❓';
    }
  };

  const getRoleColor = () => {
    switch (item.role) {
      case 'user':
        return 'cyan';
      case 'assistant':
        return 'green';
      case 'system':
        return 'yellow';
      case 'tool':
        return 'blue';
      default:
        return 'gray';
    }
  };

  return (
    <Box flexDirection="column" marginY={1}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color={getRoleColor()} bold>
          {getRoleIcon()} {item.role.charAt(0).toUpperCase() + item.role.slice(1)}
        </Text>
        <Box marginLeft={2}>
          <Text color="gray" dimColor>
            {formatTimestamp(item.timestamp)}
          </Text>
          {item.type === 'error' && (
            <Box marginLeft={2}>
              <Text color="red">
                [ERROR]
              </Text>
            </Box>
          )}
        </Box>
      </Box>

      {/* Content */}
      <Box flexDirection="column" paddingLeft={2}>
        {renderContent()}
      </Box>

      {/* Metadata */}
      {item.metadata && Object.keys(item.metadata).length > 0 && (
        <Box marginTop={1} paddingLeft={2}>
          <Text color="gray" dimColor>
            {item.metadata.command && (
              <Text>Command: {item.metadata.command} </Text>
            )}
            {item.metadata.exitCode !== undefined && (
              <Text>Exit: {item.metadata.exitCode} </Text>
            )}
            {item.metadata.duration && (
              <Text>Duration: {item.metadata.duration}ms </Text>
            )}
            {item.metadata.success !== undefined && (
              <Text color={item.metadata.success ? "green" : "red"}>
                {item.metadata.success ? "✓" : "✗"}
              </Text>
            )}
          </Text>
        </Box>
      )}
    </Box>
  );
}
