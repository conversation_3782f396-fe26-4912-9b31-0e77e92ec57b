import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
const APPROVAL_MODES = [
    {
        mode: 'suggest',
        name: 'Suggest Mode',
        description: 'Manual approval required for all commands',
        color: 'red',
        icon: '🔒',
        details: [
            '• All commands require explicit approval',
            '• Maximum security and control',
            '• Best for sensitive environments',
            '• Recommended for production systems'
        ]
    },
    {
        mode: 'auto-edit',
        name: 'Auto-Edit Mode',
        description: 'Auto-approve safe commands, ask for dangerous ones',
        color: 'yellow',
        icon: '⚡',
        details: [
            '• Safe read-only commands auto-approved',
            '• File modifications and system commands require approval',
            '• Balanced security and convenience',
            '• Good for development environments'
        ]
    },
    {
        mode: 'full-auto',
        name: 'Full-Auto Mode',
        description: 'Auto-approve all commands (use with caution)',
        color: 'green',
        icon: '🚀',
        details: [
            '• All commands executed automatically',
            '• Maximum speed and convenience',
            '• Commands still run in sandbox when available',
            '• Use only in trusted, isolated environments'
        ]
    }
];
export function ApprovalModeOverlay({ currentMode, onModeChange, onClose }) {
    const [selectedIndex, setSelectedIndex] = useState(APPROVAL_MODES.findIndex(mode => mode.mode === currentMode));
    useInput((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.return) {
            const selectedMode = APPROVAL_MODES[selectedIndex];
            if (selectedMode) {
                onModeChange(selectedMode.mode);
            }
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(prev => prev > 0 ? prev - 1 : APPROVAL_MODES.length - 1);
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(prev => prev < APPROVAL_MODES.length - 1 ? prev + 1 : 0);
            return;
        }
        // Quick selection by number
        const num = parseInt(input);
        if (num >= 1 && num <= APPROVAL_MODES.length) {
            setSelectedIndex(num - 1);
            return;
        }
    });
    const selectedMode = APPROVAL_MODES[selectedIndex];
    return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDD10 Command Approval Mode" }) }), _jsx(Box, { marginBottom: 2, paddingX: 2, borderStyle: "round", children: _jsxs(Text, { children: ["Current mode: ", _jsx(Text, { color: "cyan", bold: true, children: currentMode })] }) }), _jsx(Box, { flexDirection: "column", flexGrow: 1, children: APPROVAL_MODES.map((mode, index) => (_jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { children: _jsxs(Text, { color: index === selectedIndex ? 'cyan' : mode.color, bold: true, children: [index === selectedIndex ? '▶ ' : '  ', mode.icon, " ", mode.name, mode.mode === currentMode && ' (current)'] }) }), _jsx(Box, { paddingLeft: 4, children: _jsx(Text, { color: "gray", children: mode.description }) }), index === selectedIndex && (_jsx(Box, { flexDirection: "column", paddingLeft: 4, marginTop: 1, children: mode.details.map((detail, detailIndex) => (_jsx(Text, { color: "gray", dimColor: true, children: detail }, detailIndex))) }))] }, mode.mode))) }), selectedMode && selectedMode.mode === 'full-auto' && (_jsxs(Box, { marginY: 2, paddingX: 2, borderStyle: "round", children: [_jsx(Text, { color: "red", bold: true, children: "\u26A0\uFE0F  WARNING: Full-Auto Mode" }), _jsx(Text, { color: "red", children: "This mode will execute all AI-suggested commands automatically. Only use in trusted, isolated environments." })] })), _jsxs(Box, { marginTop: 2, flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "Use \u2191\u2193 to navigate, Enter to select, Esc to cancel" }), _jsxs(Text, { color: "gray", dimColor: true, children: ["You can also press 1-", APPROVAL_MODES.length, " for quick selection"] })] })] }));
}
//# sourceMappingURL=approval-mode-overlay.js.map