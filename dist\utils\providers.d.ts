import { ProviderConfig } from '../types/index.js';
/**
 * Built-in AI provider configurations
 * Each provider has a name, base URL, and environment variable key for API authentication
 */
export declare const providers: Record<string, ProviderConfig>;
/**
 * Get provider configuration by name
 */
export declare function getProvider(name: string): ProviderConfig | undefined;
/**
 * Get all available provider names
 */
export declare function getProviderNames(): string[];
/**
 * Check if a provider is supported
 */
export declare function isProviderSupported(name: string): boolean;
/**
 * Get default model for a provider
 */
export declare function getDefaultModel(provider: string): string;
/**
 * Get available models for a provider
 */
export declare function getProviderModels(provider: string): string[];
/**
 * Validate provider configuration
 */
export declare function validateProvider(provider: string): boolean;
/**
 * Get provider display name
 */
export declare function getProviderDisplayName(provider: string): string;
/**
 * Check if provider supports a specific model
 */
export declare function providerSupportsModel(provider: string, model: string): boolean;
/**
 * Get recommended providers for different use cases
 */
export declare const providerRecommendations: {
    coding: string[];
    general: string[];
    local: string[];
    fast: string[];
    multimodal: string[];
};
/**
 * Get providers by capability
 */
export declare function getProvidersByCapability(capability: keyof typeof providerRecommendations): string[];
//# sourceMappingURL=providers.d.ts.map