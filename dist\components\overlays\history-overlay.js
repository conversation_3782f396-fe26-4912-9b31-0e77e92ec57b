import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
export function HistoryOverlay({ items, onClose }) {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [showDetails, setShowDetails] = useState(false);
    // Filter to show only user messages and important system messages
    const displayItems = items.filter(item => item.role === 'user' ||
        (item.role === 'system' && item.type !== 'function_result') ||
        item.type === 'error');
    useInput((input, key) => {
        if (key.escape) {
            if (showDetails) {
                setShowDetails(false);
            }
            else {
                onClose();
            }
            return;
        }
        if (key.return) {
            setShowDetails(!showDetails);
            return;
        }
        if (key.upArrow && displayItems.length > 0) {
            setSelectedIndex(prev => prev > 0 ? prev - 1 : displayItems.length - 1);
            return;
        }
        if (key.downArrow && displayItems.length > 0) {
            setSelectedIndex(prev => prev < displayItems.length - 1 ? prev + 1 : 0);
            return;
        }
        if (input === 'c') {
            // Clear history (could be implemented)
            return;
        }
    });
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        return date.toLocaleString();
    };
    const getItemPreview = (item) => {
        const textContent = item.content
            .filter(c => c.type === 'text')
            .map(c => c.text)
            .join(' ');
        return textContent.length > 80
            ? textContent.substring(0, 80) + '...'
            : textContent;
    };
    const getRoleIcon = (item) => {
        switch (item.role) {
            case 'user':
                return '👤';
            case 'assistant':
                return '🤖';
            case 'system':
                return '⚙️';
            case 'tool':
                return '🔧';
            default:
                return '❓';
        }
    };
    const getRoleColor = (item) => {
        switch (item.role) {
            case 'user':
                return 'cyan';
            case 'assistant':
                return 'green';
            case 'system':
                return 'yellow';
            case 'tool':
                return 'blue';
            default:
                return 'gray';
        }
    };
    if (displayItems.length === 0) {
        return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDCDC Conversation History" }) }), _jsx(Box, { flexGrow: 1, justifyContent: "center", alignItems: "center", children: _jsx(Text, { color: "gray", children: "No conversation history yet. Start chatting to see messages here!" }) }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "gray", children: "Press Esc to close" }) })] }));
    }
    const selectedItem = displayItems[selectedIndex];
    return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsxs(Text, { color: "cyan", bold: true, children: ["\uD83D\uDCDC Conversation History (", displayItems.length, " items)"] }) }), showDetails && selectedItem ? (
            /* Detail View */
            _jsxs(Box, { flexDirection: "column", flexGrow: 1, children: [_jsxs(Box, { marginBottom: 2, children: [_jsxs(Text, { color: getRoleColor(selectedItem), bold: true, children: [getRoleIcon(selectedItem), " ", selectedItem.role.charAt(0).toUpperCase() + selectedItem.role.slice(1)] }), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "gray", children: formatTimestamp(selectedItem.timestamp) }) })] }), _jsx(Box, { flexDirection: "column", flexGrow: 1, paddingX: 2, borderStyle: "round", children: selectedItem.content.map((content, index) => {
                            switch (content.type) {
                                case 'text':
                                    return (_jsx(Text, { children: content.text }, index));
                                case 'function_call':
                                    return (_jsxs(Box, { flexDirection: "column", marginY: 1, children: [_jsxs(Text, { color: "blue", bold: true, children: ["Function Call: ", content.function_call?.name] }), _jsx(Text, { color: "gray", children: content.function_call?.arguments })] }, index));
                                default:
                                    return null;
                            }
                        }) }), selectedItem.metadata && Object.keys(selectedItem.metadata).length > 0 && (_jsxs(Box, { marginTop: 1, paddingX: 2, children: [_jsx(Text, { color: "gray", bold: true, children: "Metadata:" }), _jsx(Text, { color: "gray", children: JSON.stringify(selectedItem.metadata, null, 2) })] }))] })) : (
            /* List View */
            _jsx(Box, { flexDirection: "column", flexGrow: 1, children: displayItems.map((item, index) => (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: index === selectedIndex ? 'cyan' : getRoleColor(item), children: [index === selectedIndex ? '▶ ' : '  ', getRoleIcon(item), " ", formatTimestamp(item.timestamp), " - ", getItemPreview(item)] }) }, item.id))) })), _jsxs(Box, { marginTop: 2, flexDirection: "column", children: [_jsx(Text, { color: "gray", children: showDetails
                            ? 'Press Enter to go back to list, Esc to close'
                            : 'Use ↑↓ to navigate, Enter for details, Esc to close' }), !showDetails && (_jsxs(Text, { color: "gray", dimColor: true, children: ["Showing ", displayItems.length, " conversation items"] }))] })] }));
}
//# sourceMappingURL=history-overlay.js.map