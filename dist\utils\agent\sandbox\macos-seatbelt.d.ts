import { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
/**
 * macOS Seatbelt sandbox implementation
 * Provides system-level sandboxing using macOS Seatbelt framework
 */
/**
 * Check if Seatbelt is available on the system
 */
export declare function isAvailable(): boolean;
/**
 * Execute command with Seatbelt sandboxing
 */
export declare function execWithSeatbelt(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, abortSignal?: AbortSignal): Promise<ExecResult>;
/**
 * Get Seatbelt capabilities
 */
export declare function getCapabilities(): {
    sandboxed: boolean;
    networkRestricted: boolean;
    filesystemRestricted: boolean;
    processRestricted: boolean;
};
/**
 * Get security level description
 */
export declare function getSecurityLevel(): string;
/**
 * Test Seatbelt functionality
 */
export declare function testSeatbelt(): Promise<{
    available: boolean;
    version?: string;
    features?: string[];
    error?: string;
}>;
/**
 * Get macOS version information
 */
export declare function getMacOSVersion(): string | null;
/**
 * Check if System Integrity Protection (SIP) is enabled
 */
export declare function isSIPEnabled(): boolean;
//# sourceMappingURL=macos-seatbelt.d.ts.map