import { Session, ResponseItem, AppConfig } from '../types';
/**
 * Session metadata for listing
 */
export interface SessionMetadata {
    id: string;
    name: string;
    created: number;
    updated: number;
    messageCount: number;
    model: string;
    provider: string;
    size: number;
}
/**
 * Session search options
 */
export interface SessionSearchOptions {
    query?: string;
    model?: string;
    provider?: string;
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    sortBy?: 'created' | 'updated' | 'name' | 'messageCount';
    sortOrder?: 'asc' | 'desc';
}
/**
 * Session export options
 */
export interface SessionExportOptions {
    format: 'json' | 'markdown' | 'txt';
    includeMetadata?: boolean;
    includeSystemMessages?: boolean;
    outputPath?: string;
}
/**
 * Session manager class
 */
export declare class SessionManager {
    private sessionsDir;
    constructor(customDir?: string);
    /**
     * Initialize session storage
     */
    initialize(): Promise<void>;
    /**
     * Create a new session
     */
    createSession(name: string, config: AppConfig, initialItems?: ResponseItem[]): Promise<Session>;
    /**
     * Save session to storage
     */
    saveSession(session: Session): Promise<void>;
    /**
     * Load session from storage
     */
    loadSession(sessionId: string): Promise<Session | null>;
    /**
     * Delete session from storage
     */
    deleteSession(sessionId: string): Promise<boolean>;
    /**
     * List all sessions with metadata
     */
    listSessions(): Promise<SessionMetadata[]>;
    /**
     * Search sessions
     */
    searchSessions(options?: SessionSearchOptions): Promise<SessionMetadata[]>;
    /**
     * Update session items
     */
    updateSessionItems(sessionId: string, items: ResponseItem[]): Promise<boolean>;
    /**
     * Add items to session
     */
    addItemsToSession(sessionId: string, newItems: ResponseItem[]): Promise<boolean>;
    /**
     * Export session to different formats
     */
    exportSession(sessionId: string, options: SessionExportOptions): Promise<string | null>;
    /**
     * Get session statistics
     */
    getSessionStats(): Promise<{
        totalSessions: number;
        totalMessages: number;
        totalSize: number;
        oldestSession: number;
        newestSession: number;
        providerBreakdown: Record<string, number>;
        modelBreakdown: Record<string, number>;
    }>;
    /**
     * Clean up old sessions
     */
    cleanupOldSessions(olderThanDays: number): Promise<number>;
    /**
     * Validate session structure
     */
    private isValidSession;
    /**
     * Format session as markdown
     */
    private formatSessionAsMarkdown;
    /**
     * Format session as plain text
     */
    private formatSessionAsText;
}
/**
 * Default session manager instance
 */
export declare const sessionManager: SessionManager;
//# sourceMappingURL=session-manager.d.ts.map