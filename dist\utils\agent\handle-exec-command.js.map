{"version": 3, "file": "handle-exec-command.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/handle-exec-command.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAuE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAC1H,OAAO,EAAE,aAAa,EAAE,sBAAsB,EAAE,MAAM,oBAAoB,CAAC;AAC3E,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AASnG;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,IAA+D,EAC/D,MAAiB,EACjB,cAA8B,EAC9B,0BAAiD,EAAE,EACnD,sBAA2D,EAC3D,MAAoB;IAEpB,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC;IAEvG,mBAAmB;IACnB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,IAAI,aAAa,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;IAED,4BAA4B;IAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE9C,4BAA4B;IAC5B,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC;IAC3F,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACtB,MAAM,IAAI,aAAa,CAAC,UAAU,CAAC,KAAK,IAAI,2BAA2B,CAAC,CAAC;IAC3E,CAAC;IAED,gCAAgC;IAChC,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAExE,IAAI,gBAAgB,IAAI,sBAAsB,EAAE,CAAC;QAC/C,MAAM,eAAe,GAAG,qBAAqB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QACxE,MAAM,QAAQ,GAAG,MAAM,sBAAsB,CAAC,eAAe,CAAC,CAAC;QAE/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,UAAU,EAAE,qCAAqC;gBACjD,QAAQ,EAAE;oBACR,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC1B,OAAO,EAAE,eAAe;iBACzB;gBACD,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,MAAM,SAAS,GAAc;QAC3B,OAAO;QACP,OAAO,EAAE,eAAe;QACxB,OAAO;QACP,GAAG,EAAE;YACH,2CAA2C;YAC3C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;YAC5B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;YACvD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE;YACpD,mDAAmD;YACnD,GAAG,MAAM,CAAC,GAAG;SACd;KACF,CAAC;IAEF,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAEvF,gBAAgB;QAChB,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE5C,mBAAmB;QACnB,MAAM,QAAQ,GAAG;YACf,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;YACjC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE,sBAAsB,EAAE,CAAC,IAAI;YACtC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,wDAAwD;QACxD,MAAM,eAAe,GAAwB,EAAE,CAAC;QAEhD,gEAAgE;QAChE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC9D,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,eAAe,CAAC,IAAI,CAAC;oBACnB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,YAAY;4BAClB,IAAI,EAAE,oBAAoB,MAAM,CAAC,MAAM,EAAE;yBAC1C,CAAC;oBACF,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,eAAe,CAAC,IAAI,CAAC;oBACnB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,YAAY;4BAClB,IAAI,EAAE,oBAAoB,MAAM,CAAC,MAAM,EAAE;yBAC1C,CAAC;oBACF,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,UAAU;YACV,QAAQ;YACR,eAAe;SAChB,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,0BAA0B;QAC1B,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC;QAExF,OAAO;YACL,UAAU,EAAE,6BAA6B,YAAY,EAAE;YACvD,QAAQ,EAAE;gBACR,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC1B,OAAO,EAAE,eAAe;gBACxB,KAAK,EAAE,YAAY;gBACnB,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB;YACD,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,MAAkB;IAC1C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;IAExF,IAAI,MAAM,GAAG,YAAY,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;IAC/C,MAAM,IAAI,sBAAsB,OAAO,IAAI,CAAC;IAC5C,MAAM,IAAI,cAAc,QAAQ,IAAI,CAAC;IACrC,MAAM,IAAI,aAAa,QAAQ,MAAM,CAAC;IACtC,MAAM,IAAI,WAAW,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC;IAExD,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,IAAI,cAAc,MAAM,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,IAAI,cAAc,MAAM,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAI,YAAY,KAAK,EAAE,CAAC;IAChC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,aAAqB;IACrD,8DAA8D;IAC9D,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC9C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YAChD,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;aAAM,IAAI,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YAC1C,QAAQ,GAAG,KAAK,CAAC;YACjB,SAAS,GAAG,EAAE,CAAC;QACjB,CAAC;aAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACrC,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnB,OAAO,GAAG,EAAE,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,IAAI,CAAC;QAClB,CAAC;IACH,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,wBAAwB,CAAC,OAAe,EAAE,YAAmC;IACpF,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE9C,2CAA2C;IAC3C,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,eAAe,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAkB;IACpD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;IAExD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACnC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAErC,OAAO,GAAG,MAAM,IAAI,UAAU,KAAK,QAAQ,aAAa,QAAQ,GAAG,CAAC;AACtE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,OAAiB;IACpD,MAAM,mBAAmB,GAAG;QAC1B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK;QAC1C,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;QAC7C,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM;KACpC,CAAC;IAEF,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;IAC9C,OAAO,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CAAC,OAAiB;IAC3D,MAAM,WAAW,GAAa,EAAE,CAAC;IACjC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IAEnD,kEAAkE;IAClE,IAAI,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjG,WAAW,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,6CAA6C;IAC7C,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACtD,WAAW,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAChE,CAAC;IAED,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACtF,WAAW,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IACpE,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAAiB;IACrD,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7D,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAEjF,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;IAE9C,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,OAAe;IAC5C,MAAM,SAAS,GAA2B;QACxC,IAAI,EAAE,6EAA6E;QACnF,KAAK,EAAE,wDAAwD;QAC/D,MAAM,EAAE,8DAA8D;QACtE,MAAM,EAAE,iEAAiE;QACzE,KAAK,EAAE,uEAAuE;QAC9E,KAAK,EAAE,uEAAuE;QAC9E,KAAK,EAAE,gEAAgE;QACvE,QAAQ,EAAE,+DAA+D;KAC1E,CAAC;IAEF,OAAO,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,kCAAkC,OAAO,EAAE,CAAC;AACzF,CAAC"}