{"version": 3, "file": "error-handling.js", "sourceRoot": "", "sources": ["../../src/utils/error-handling.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AA4DjD;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAgB;IAC/C,WAAW,EAAE,CAAC;IACd,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,KAAK;IACf,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;QACf,YAAY;QACZ,WAAW;QACX,cAAc;QACd,WAAW;QACX,qBAAqB;QACrB,qBAAqB;KACtB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,YAAY;IACf,QAAQ,GAAsB,EAAE,CAAC;IACjC,UAAU,GAAG,IAAI,CAAC;IAE1B;;OAEG;IACH,YAAY,CACV,KAAsB,EACtB,OAA8B;QAE9B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,EAAE,GAAG,SAAS,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QAE/E,IAAI,aAAoB,CAAC;QACzB,IAAI,OAAe,CAAC;QACpB,IAAI,KAAyB,CAAC;QAE9B,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,aAAa,GAAG,KAAK,CAAC;YACtB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YACxB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACxB,aAAa,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAElD,MAAM,eAAe,GAAoB;YACvC,EAAE;YACF,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,OAAO,EAAE;gBACP,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS;gBACzC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS;gBACzC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS;gBACT,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B;YACD,aAAa;YACb,KAAK;YACL,WAAW;YACX,SAAS;YACT,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,QAAQ,CAAC;YAC9D,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,QAAQ,CAAC;SACzE,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC/B,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAY;QAClC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEtC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC7D,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,YAAY,EAAE,CAAC;YAC9D,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACtE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7D,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC7D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAChE,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YAC/D,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACtC,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAY,EAAE,QAAuB;QAC1D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,kBAAkB;QAClB,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzD,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,uBAAuB;QACvB,IAAI,QAAQ,KAAK,gBAAgB,IAAI,QAAQ,KAAK,YAAY;YAC1D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACnE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,yBAAyB;QACzB,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,UAAU;YACjD,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACnE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,sBAAsB;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAY,EAAE,QAAuB;QACzD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAE5C,yBAAyB;QACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;YACzD,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ,KAAK,YAAY,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,qBAAqB;QACrB,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,UAAU;YACjD,QAAQ,KAAK,YAAY,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAY;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAI,KAAa,CAAC,IAAI,CAAC;QAEjC,MAAM,iBAAiB,GAAG;YACxB,SAAS;YACT,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,qBAAqB;YACrB,WAAW;YACX,OAAO;SACR,CAAC;QAEF,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,WAAW;YACX,cAAc;YACd,WAAW;YACX,qBAAqB;SACtB,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC5D,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,KAAY,EAAE,QAAuB;QAC/D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,gFAAgF,CAAC;YAE1F,KAAK,gBAAgB;gBACnB,OAAO,iEAAiE,CAAC;YAE3E,KAAK,YAAY;gBACf,OAAO,gEAAgE,CAAC;YAE1E,KAAK,YAAY;gBACf,OAAO,8DAA8D,CAAC;YAExE,KAAK,UAAU;gBACb,OAAO,4DAA4D,CAAC;YAEtE,KAAK,QAAQ;gBACX,OAAO,mFAAmF,CAAC;YAE7F,KAAK,MAAM;gBACT,OAAO,wCAAwC,CAAC;YAElD;gBACE,OAAO,iDAAiD,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,KAAY,EAAE,QAAuB;QACpE,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,KAAsB;QACrC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,0BAA0B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC;QAED,mCAAmC;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpD,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,gBAAgB,EAAE,EAAE;YACnD,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,QAAuB;QAC1C,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU,CAAC;YAChB,KAAK,MAAM;gBACT,OAAO,OAAO,CAAC,KAAK,CAAC;YACvB,KAAK,QAAQ;gBACX,OAAO,OAAO,CAAC,IAAI,CAAC;YACtB,KAAK,KAAK,CAAC;YACX;gBACE,OAAO,OAAO,CAAC,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB,EAAE;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAuB;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAuB;QACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,SAA2B,EAC3B,SAA+B,EAAE;IAEjC,MAAM,WAAW,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,MAAM,EAAE,CAAC;IAC3D,IAAI,SAAgB,CAAC;IAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;QACpE,IAAI,CAAC;YACH,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAEtE,8BAA8B;YAC9B,MAAM,WAAW,GAAG,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CACpE,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACzC,SAAiB,CAAC,IAAI,KAAK,cAAc,CAC3C,CAAC;YAEF,IAAI,CAAC,WAAW,IAAI,OAAO,KAAK,WAAW,CAAC,WAAW,EAAE,CAAC;gBACxD,MAAM,SAAS,CAAC;YAClB,CAAC;YAED,2CAA2C;YAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,CAAC,EACxE,WAAW,CAAC,QAAQ,CACrB,CAAC;YAEF,wCAAwC;YACxC,MAAM,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;YAEnD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,MAAM,SAAU,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,cAAc;IAMf;IACA;IANF,QAAQ,GAAG,CAAC,CAAC;IACb,eAAe,GAAG,CAAC,CAAC;IACpB,KAAK,GAAoC,QAAQ,CAAC;IAE1D,YACU,mBAA2B,CAAC,EAC5B,kBAA0B,KAAK;QAD/B,qBAAgB,GAAhB,gBAAgB,CAAY;QAC5B,oBAAe,GAAf,eAAe,CAAgB;IACtC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC7D,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACxB,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACtB,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACxB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAE/C;;GAEG;AACH,MAAM,UAAU,wBAAwB;IACtC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;QACxC,MAAM,eAAe,GAAG,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE;YACvD,SAAS,EAAE,oBAAoB;YAC/B,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;QACtD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;QACnD,MAAM,eAAe,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE;YACxD,SAAS,EAAE,qBAAqB;YAChC,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;AACL,CAAC"}