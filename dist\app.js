import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { TerminalChat } from './components/chat/terminal-chat.js';
import { isInGitRepository, getGitRepositoryRoot } from './utils/config.js';
import { getSandboxCapabilities } from './utils/agent/sandbox/index.js';
export function App({ config, initialMessage, verbose }) {
    const { exit } = useApp();
    const [isReady, setIsReady] = useState(false);
    const [error, setError] = useState(null);
    const [warnings, setWarnings] = useState([]);
    useEffect(() => {
        async function initialize() {
            try {
                const newWarnings = [];
                // Check if we're in a git repository
                if (!isInGitRepository()) {
                    newWarnings.push('Not in a git repository - some features may be limited');
                }
                // Check sandbox capabilities
                const sandbox = getSandboxCapabilities();
                if (!sandbox.sandboxed && verbose) {
                    newWarnings.push(`Using ${sandbox.name} - limited security isolation`);
                }
                // Validate working directory
                if (config.workdir) {
                    const fs = require('fs');
                    if (!fs.existsSync(config.workdir)) {
                        throw new Error(`Working directory does not exist: ${config.workdir}`);
                    }
                }
                setWarnings(newWarnings);
                setIsReady(true);
            }
            catch (err) {
                setError(err instanceof Error ? err.message : 'Initialization failed');
            }
        }
        initialize();
    }, [config, verbose]);
    // Handle app exit
    const handleExit = () => {
        exit();
    };
    if (error) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsxs(Text, { color: "red", children: ["\u274C Error: ", error] }), _jsx(Text, { color: "gray", children: "Press Ctrl+C to exit" })] }));
    }
    if (!isReady) {
        return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { color: "blue", children: "\uD83D\uDE80 Initializing Kritrima AI..." }), _jsx(Text, { color: "gray", children: "Loading configuration and checking environment..." })] }));
    }
    return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "cyan", bold: true, children: "\uD83E\uDD16 Kritrima AI CLI" }), _jsxs(Text, { color: "gray", children: ["Model: ", config.model, " | Provider: ", config.provider, " | Mode: ", config.approvalMode] }), warnings.length > 0 && (_jsx(Box, { flexDirection: "column", marginTop: 1, children: warnings.map((warning, index) => (_jsxs(Text, { color: "yellow", children: ["\u26A0\uFE0F  ", warning] }, index))) })), isInGitRepository() && verbose && (_jsxs(Text, { color: "green", children: ["\uD83D\uDCC1 Git repository: ", getGitRepositoryRoot()] })), verbose && (_jsxs(Text, { color: "blue", children: ["\uD83D\uDD12 Security: ", getSandboxCapabilities().name] }))] }), _jsx(Box, { flexGrow: 1, children: _jsx(TerminalChat, { config: config, initialMessage: initialMessage, onExit: handleExit }) }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "Press Ctrl+C to exit | Type /help for commands | Type /model to switch models" }) })] }));
}
export class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('React Error Boundary caught an error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback;
            return _jsx(FallbackComponent, { error: this.state.error });
        }
        return this.props.children;
    }
}
/**
 * Default error fallback component
 */
function DefaultErrorFallback({ error }) {
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { color: "red", bold: true, children: "\uD83D\uDCA5 Application Error" }), _jsx(Text, { color: "red", children: error.message }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: "Please check your configuration and try again." }) }), _jsx(Text, { color: "gray", children: "Press Ctrl+C to exit" })] }));
}
/**
 * App wrapper with error boundary
 */
export function AppWithErrorBoundary(props) {
    return (_jsx(ErrorBoundary, { children: _jsx(App, { ...props }) }));
}
/**
 * Welcome message component
 */
export function WelcomeMessage({ config }) {
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Text, { color: "cyan", bold: true, children: "Welcome to Kritrima AI! \uD83D\uDE80" }), _jsxs(Text, { children: ["You're connected to ", config.provider, " using ", config.model] }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: "Available commands:" }) }), _jsx(Text, { color: "gray", children: "\u2022 /help - Show help information" }), _jsx(Text, { color: "gray", children: "\u2022 /model - Switch AI model or provider" }), _jsx(Text, { color: "gray", children: "\u2022 /history - View command history" }), _jsx(Text, { color: "gray", children: "\u2022 /clear - Clear conversation" }), _jsx(Text, { color: "gray", children: "\u2022 /exit - Exit the application" }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "green", children: "Start typing your message below..." }) })] }));
}
/**
 * Loading component
 */
export function LoadingSpinner({ text = "Loading..." }) {
    const [frame, setFrame] = useState(0);
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    useEffect(() => {
        const interval = setInterval(() => {
            setFrame(prev => (prev + 1) % frames.length);
        }, 100);
        return () => clearInterval(interval);
    }, []);
    return (_jsx(Box, { children: _jsxs(Text, { color: "blue", children: [frames[frame], " ", text] }) }));
}
/**
 * Status indicator component
 */
export function StatusIndicator({ status, message }) {
    const getStatusIcon = () => {
        switch (status) {
            case 'success':
                return '✅';
            case 'error':
                return '❌';
            case 'warning':
                return '⚠️';
            case 'info':
            default:
                return 'ℹ️';
        }
    };
    const getStatusColor = () => {
        switch (status) {
            case 'success':
                return 'green';
            case 'error':
                return 'red';
            case 'warning':
                return 'yellow';
            case 'info':
            default:
                return 'blue';
        }
    };
    return (_jsx(Box, { children: _jsxs(Text, { color: getStatusColor(), children: [getStatusIcon(), " ", message] }) }));
}
//# sourceMappingURL=app.js.map