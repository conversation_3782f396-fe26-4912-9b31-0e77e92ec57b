import React from 'react';
import { Box, Text, useInput } from 'ink';

interface HelpOverlayProps {
  onClose: () => void;
}

export function HelpOverlay({ onClose }: HelpOverlayProps) {
  useInput((input, key) => {
    if (key.escape || key.return || input === 'q') {
      onClose();
    }
  });

  return (
    <Box flexDirection="column" height="100%" padding={2}>
      {/* Header */}
      <Box marginBottom={2}>
        <Text color="cyan" bold>
          📚 Kritrima AI CLI Help
        </Text>
      </Box>

      {/* Commands Section */}
      <Box flexDirection="column" marginBottom={2}>
        <Box marginBottom={1}>
          <Text color="yellow" bold>
            Slash Commands:
          </Text>
        </Box>
        
        <Box flexDirection="column" paddingLeft={2}>
          <Text>
            <Text color="green">/help</Text> - Show this help information
          </Text>
          <Text>
            <Text color="green">/model</Text> - Switch AI model or provider
          </Text>
          <Text>
            <Text color="green">/history</Text> - View conversation history
          </Text>
          <Text>
            <Text color="green">/clear</Text> - Clear current conversation
          </Text>
          <Text>
            <Text color="green">/approval</Text> - Change command approval mode
          </Text>
          <Text>
            <Text color="green">/status</Text> - Show current status and settings
          </Text>
          <Text>
            <Text color="green">/exit</Text> - Exit the application
          </Text>
        </Box>
      </Box>

      {/* Keyboard Shortcuts */}
      <Box flexDirection="column" marginBottom={2}>
        <Box marginBottom={1}>
          <Text color="yellow" bold>
            Keyboard Shortcuts:
          </Text>
        </Box>
        
        <Box flexDirection="column" paddingLeft={2}>
          <Text>
            <Text color="blue">Ctrl+C</Text> - Exit application
          </Text>
          <Text>
            <Text color="blue">Ctrl+M</Text> - Open model selection
          </Text>
          <Text>
            <Text color="blue">Ctrl+H</Text> - Show help (this screen)
          </Text>
          <Text>
            <Text color="blue">Ctrl+R</Text> - View history
          </Text>
          <Text>
            <Text color="blue">Esc</Text> - Close overlays
          </Text>
          <Text>
            <Text color="blue">↑↓</Text> - Navigate command history
          </Text>
          <Text>
            <Text color="blue">Tab</Text> - Autocomplete commands/files
          </Text>
        </Box>
      </Box>

      {/* File References */}
      <Box flexDirection="column" marginBottom={2}>
        <Box marginBottom={1}>
          <Text color="yellow" bold>
            File References:
          </Text>
        </Box>
        
        <Box flexDirection="column" paddingLeft={2}>
          <Text>
            Use <Text color="green">@filename</Text> to reference files in your messages
          </Text>
          <Text>
            Example: "Analyze the code in <Text color="green">@src/app.tsx</Text>"
          </Text>
          <Text>
            Tab completion works for file paths after @
          </Text>
        </Box>
      </Box>

      {/* Approval Modes */}
      <Box flexDirection="column" marginBottom={2}>
        <Box marginBottom={1}>
          <Text color="yellow" bold>
            Approval Modes:
          </Text>
        </Box>
        
        <Box flexDirection="column" paddingLeft={2}>
          <Text>
            <Text color="red">suggest</Text> - Manual approval for all commands
          </Text>
          <Text>
            <Text color="yellow">auto-edit</Text> - Auto-approve safe commands, ask for others
          </Text>
          <Text>
            <Text color="green">full-auto</Text> - Auto-approve all commands (use with caution)
          </Text>
        </Box>
      </Box>

      {/* Tips */}
      <Box flexDirection="column" marginBottom={2}>
        <Box marginBottom={1}>
          <Text color="yellow" bold>
            Tips:
          </Text>
        </Box>
        
        <Box flexDirection="column" paddingLeft={2}>
          <Text>
            • The AI can execute shell commands and modify files
          </Text>
          <Text>
            • Commands are sandboxed for security when possible
          </Text>
          <Text>
            • Use descriptive messages for better AI responses
          </Text>
          <Text>
            • Check approval mode if commands aren't executing
          </Text>
          <Text>
            • Git integration provides better context awareness
          </Text>
        </Box>
      </Box>

      {/* Footer */}
      <Box marginTop={2}>
        <Text color="gray">
          Press Esc, Enter, or 'q' to close this help screen
        </Text>
      </Box>
    </Box>
  );
}
