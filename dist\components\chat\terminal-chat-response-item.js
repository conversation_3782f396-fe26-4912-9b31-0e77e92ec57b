import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export function TerminalChatResponseItem({ item }) {
    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString();
    };
    const renderContent = () => {
        return item.content.map((content, index) => {
            switch (content.type) {
                case 'text':
                    return (_jsx(Box, { flexDirection: "column", children: _jsx(Text, { children: content.text }) }, index));
                case 'function_call':
                    if (content.function_call) {
                        return (_jsxs(Box, { flexDirection: "column", marginY: 1, paddingX: 2, borderStyle: "round", children: [_jsxs(Text, { color: "blue", bold: true, children: ["\uD83D\uDD27 Function Call: ", content.function_call.name] }), _jsxs(Text, { color: "gray", children: ["Arguments: ", content.function_call.arguments] })] }, index));
                    }
                    break;
                case 'function_result':
                    if (content.function_result) {
                        return (_jsxs(Box, { flexDirection: "column", marginY: 1, paddingX: 2, borderStyle: "round", children: [_jsxs(Text, { color: content.function_result.success ? "green" : "red", bold: true, children: [content.function_result.success ? "✅" : "❌", " Function Result"] }), _jsx(Text, { children: content.function_result.result }), content.function_result.metadata && (_jsxs(Text, { color: "gray", dimColor: true, children: ["Metadata: ", JSON.stringify(content.function_result.metadata, null, 2)] }))] }, index));
                    }
                    break;
                default:
                    return null;
            }
        });
    };
    const getRoleIcon = () => {
        switch (item.role) {
            case 'user':
                return '👤';
            case 'assistant':
                return '🤖';
            case 'system':
                return '⚙️';
            case 'tool':
                return '🔧';
            default:
                return '❓';
        }
    };
    const getRoleColor = () => {
        switch (item.role) {
            case 'user':
                return 'cyan';
            case 'assistant':
                return 'green';
            case 'system':
                return 'yellow';
            case 'tool':
                return 'blue';
            default:
                return 'gray';
        }
    };
    return (_jsxs(Box, { flexDirection: "column", marginY: 1, children: [_jsxs(Box, { marginBottom: 1, children: [_jsxs(Text, { color: getRoleColor(), bold: true, children: [getRoleIcon(), " ", item.role.charAt(0).toUpperCase() + item.role.slice(1)] }), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { color: "gray", dimColor: true, children: formatTimestamp(item.timestamp) }), item.type === 'error' && (_jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "red", children: "[ERROR]" }) }))] })] }), _jsx(Box, { flexDirection: "column", paddingLeft: 2, children: renderContent() }), item.metadata && Object.keys(item.metadata).length > 0 && (_jsx(Box, { marginTop: 1, paddingLeft: 2, children: _jsxs(Text, { color: "gray", dimColor: true, children: [item.metadata.command && (_jsxs(Text, { children: ["Command: ", item.metadata.command, " "] })), item.metadata.exitCode !== undefined && (_jsxs(Text, { children: ["Exit: ", item.metadata.exitCode, " "] })), item.metadata.duration && (_jsxs(Text, { children: ["Duration: ", item.metadata.duration, "ms "] })), item.metadata.success !== undefined && (_jsx(Text, { color: item.metadata.success ? "green" : "red", children: item.metadata.success ? "✓" : "✗" }))] }) }))] }));
}
//# sourceMappingURL=terminal-chat-response-item.js.map