import { ProviderConfig } from '../types/index.js';

/**
 * Built-in AI provider configurations
 * Each provider has a name, base URL, and environment variable key for API authentication
 */
export const providers: Record<string, ProviderConfig> = {
  openai: {
    name: "OpenAI",
    baseURL: "https://api.openai.com/v1",
    envKey: "OPENAI_API_KEY",
    models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"],
    defaultModel: "gpt-4"
  },
  
  azure: {
    name: "Azure OpenAI",
    baseURL: "https://your-resource.openai.azure.com",
    envKey: "AZURE_OPENAI_API_KEY",
    models: ["gpt-4", "gpt-35-turbo"],
    defaultModel: "gpt-4"
  },
  
  gemini: {
    name: "Google Gemini",
    baseURL: "https://generativelanguage.googleapis.com/v1beta",
    envKey: "GEMINI_API_KEY",
    models: ["gemini-pro", "gemini-pro-vision"],
    defaultModel: "gemini-pro"
  },
  
  ollama: {
    name: "Ollama",
    baseURL: "http://localhost:11434/v1",
    envKey: "OLLAMA_API_KEY",
    models: ["llama2", "codellama", "mistral", "neural-chat"],
    defaultModel: "llama2"
  },
  
  mistral: {
    name: "Mistral AI",
    baseURL: "https://api.mistral.ai/v1",
    envKey: "MISTRAL_API_KEY",
    models: ["mistral-tiny", "mistral-small", "mistral-medium", "mistral-large"],
    defaultModel: "mistral-small"
  },
  
  deepseek: {
    name: "DeepSeek",
    baseURL: "https://api.deepseek.com/v1",
    envKey: "DEEPSEEK_API_KEY",
    models: ["deepseek-chat", "deepseek-coder"],
    defaultModel: "deepseek-chat"
  },
  
  xai: {
    name: "xAI",
    baseURL: "https://api.x.ai/v1",
    envKey: "XAI_API_KEY",
    models: ["grok-beta"],
    defaultModel: "grok-beta"
  },
  
  groq: {
    name: "Groq",
    baseURL: "https://api.groq.com/openai/v1",
    envKey: "GROQ_API_KEY",
    models: ["mixtral-8x7b-32768", "llama2-70b-4096"],
    defaultModel: "mixtral-8x7b-32768"
  },
  
  arceeai: {
    name: "ArceeAI",
    baseURL: "https://api.arcee.ai/v1",
    envKey: "ARCEEAI_API_KEY",
    models: ["arcee-nova", "arcee-spark"],
    defaultModel: "arcee-nova"
  },
  
  openrouter: {
    name: "OpenRouter",
    baseURL: "https://openrouter.ai/api/v1",
    envKey: "OPENROUTER_API_KEY",
    models: [
      "openai/gpt-4",
      "anthropic/claude-3-opus",
      "meta-llama/llama-2-70b-chat",
      "mistralai/mixtral-8x7b-instruct"
    ],
    defaultModel: "openai/gpt-4"
  }
};

/**
 * Get provider configuration by name
 */
export function getProvider(name: string): ProviderConfig | undefined {
  return providers[name.toLowerCase()];
}

/**
 * Get all available provider names
 */
export function getProviderNames(): string[] {
  return Object.keys(providers);
}

/**
 * Check if a provider is supported
 */
export function isProviderSupported(name: string): boolean {
  return name.toLowerCase() in providers;
}

/**
 * Get default model for a provider
 */
export function getDefaultModel(provider: string): string {
  const providerConfig = getProvider(provider);
  return providerConfig?.defaultModel || "gpt-4";
}

/**
 * Get available models for a provider
 */
export function getProviderModels(provider: string): string[] {
  const providerConfig = getProvider(provider);
  return providerConfig?.models || [];
}

/**
 * Validate provider configuration
 */
export function validateProvider(provider: string): boolean {
  const config = getProvider(provider);
  if (!config) return false;
  
  return !!(config.name && config.baseURL && config.envKey);
}

/**
 * Get provider display name
 */
export function getProviderDisplayName(provider: string): string {
  const config = getProvider(provider);
  return config?.name || provider;
}

/**
 * Check if provider supports a specific model
 */
export function providerSupportsModel(provider: string, model: string): boolean {
  const models = getProviderModels(provider);
  return models.length === 0 || models.includes(model);
}

/**
 * Get recommended providers for different use cases
 */
export const providerRecommendations = {
  coding: ["openai", "deepseek", "mistral"],
  general: ["openai", "gemini", "mistral"],
  local: ["ollama"],
  fast: ["groq", "openai"],
  multimodal: ["openai", "gemini"]
};

/**
 * Get providers by capability
 */
export function getProvidersByCapability(capability: keyof typeof providerRecommendations): string[] {
  return providerRecommendations[capability] || [];
}
