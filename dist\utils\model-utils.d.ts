import { ModelInfo } from '../types/index.js';
/**
 * Fetch available models from a provider with caching
 */
export declare function fetchModels(provider: string): Promise<string[]>;
/**
 * Get model information with fallback to API discovery
 */
export declare function getModelInfoWithFallback(modelId: string, provider: string): Promise<ModelInfo | null>;
/**
 * Validate model compatibility with provider
 */
export declare function validateModelProvider(modelId: string, provider: string): boolean;
/**
 * Get recommended model for a provider
 */
export declare function getRecommendedModel(provider: string): string;
/**
 * Calculate context usage for a conversation
 */
export declare function calculateContextUsage(messages: Array<{
    content: string;
}>, modelId: string): {
    used: number;
    total: number;
    percentage: number;
};
/**
 * Check if context is approaching limit
 */
export declare function isContextNearLimit(messages: Array<{
    content: string;
}>, modelId: string, threshold?: number): boolean;
/**
 * Suggest context optimization strategies
 */
export declare function suggestContextOptimization(messages: Array<{
    content: string;
}>, modelId: string): string[];
/**
 * Clear model cache
 */
export declare function clearModelCache(): void;
/**
 * Get cached models without API call
 */
export declare function getCachedModels(provider: string): string[] | null;
/**
 * Preload models for common providers
 */
export declare function preloadModels(providers?: string[]): Promise<void>;
//# sourceMappingURL=model-utils.d.ts.map