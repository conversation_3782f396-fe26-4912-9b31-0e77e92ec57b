import { NetworkError } from '../types/index.js';

/**
 * Error severity levels
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Error categories
 */
export type ErrorCategory = 
  | 'network'
  | 'authentication'
  | 'validation'
  | 'permission'
  | 'resource'
  | 'system'
  | 'user'
  | 'unknown';

/**
 * Error context information
 */
export interface ErrorContext {
  operation: string;
  component: string;
  userId?: string;
  sessionId?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Structured error information
 */
export interface StructuredError {
  id: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context: ErrorContext;
  originalError?: Error;
  stack?: string;
  recoverable: boolean;
  retryable: boolean;
  userMessage: string;
  technicalMessage: string;
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: string[];
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffFactor: 2,
  retryableErrors: [
    'ECONNRESET',
    'ENOTFOUND',
    'ECONNREFUSED',
    'ETIMEDOUT',
    'RATE_LIMIT_EXCEEDED',
    'SERVICE_UNAVAILABLE'
  ]
};

/**
 * Error handler class
 */
export class ErrorHandler {
  private errorLog: StructuredError[] = [];
  private maxLogSize = 1000;

  /**
   * Process and structure an error
   */
  processError(
    error: Error | unknown,
    context: Partial<ErrorContext>
  ): StructuredError {
    const timestamp = Date.now();
    const id = `error_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;
    
    let originalError: Error;
    let message: string;
    let stack: string | undefined;

    if (error instanceof Error) {
      originalError = error;
      message = error.message;
      stack = error.stack;
    } else {
      message = String(error);
      originalError = new Error(message);
    }

    const category = this.categorizeError(originalError);
    const severity = this.assessSeverity(originalError, category);
    const recoverable = this.isRecoverable(originalError, category);
    const retryable = this.isRetryable(originalError);

    const structuredError: StructuredError = {
      id,
      message,
      category,
      severity,
      context: {
        operation: context.operation || 'unknown',
        component: context.component || 'unknown',
        userId: context.userId,
        sessionId: context.sessionId,
        timestamp,
        metadata: context.metadata
      },
      originalError,
      stack,
      recoverable,
      retryable,
      userMessage: this.generateUserMessage(originalError, category),
      technicalMessage: this.generateTechnicalMessage(originalError, category)
    };

    this.logError(structuredError);
    return structuredError;
  }

  /**
   * Categorize error type
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (message.includes('network') || message.includes('connection') || 
        name.includes('network') || error instanceof NetworkError) {
      return 'network';
    }

    if (message.includes('unauthorized') || message.includes('authentication') ||
        message.includes('api key') || message.includes('token')) {
      return 'authentication';
    }

    if (message.includes('validation') || message.includes('invalid') ||
        message.includes('required') || name.includes('validation')) {
      return 'validation';
    }

    if (message.includes('permission') || message.includes('forbidden') ||
        message.includes('access denied')) {
      return 'permission';
    }

    if (message.includes('not found') || message.includes('missing') ||
        message.includes('resource')) {
      return 'resource';
    }

    if (message.includes('system') || message.includes('internal') ||
        name.includes('system')) {
      return 'system';
    }

    if (message.includes('user') || message.includes('input')) {
      return 'user';
    }

    return 'unknown';
  }

  /**
   * Assess error severity
   */
  private assessSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    const message = error.message.toLowerCase();

    // Critical errors
    if (message.includes('critical') || message.includes('fatal') ||
        category === 'system' && message.includes('crash')) {
      return 'critical';
    }

    // High severity errors
    if (category === 'authentication' || category === 'permission' ||
        message.includes('security') || message.includes('corruption')) {
      return 'high';
    }

    // Medium severity errors
    if (category === 'network' || category === 'resource' ||
        message.includes('timeout') || message.includes('unavailable')) {
      return 'medium';
    }

    // Low severity errors
    return 'low';
  }

  /**
   * Check if error is recoverable
   */
  private isRecoverable(error: Error, category: ErrorCategory): boolean {
    const message = error.message.toLowerCase();

    // Non-recoverable errors
    if (message.includes('fatal') || message.includes('critical') ||
        message.includes('corruption') || category === 'permission') {
      return false;
    }

    // Recoverable errors
    if (category === 'network' || category === 'resource' ||
        category === 'validation' || category === 'user') {
      return true;
    }

    return false;
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(error: Error): boolean {
    const message = error.message.toLowerCase();
    const code = (error as any).code;

    const retryablePatterns = [
      'timeout',
      'connection',
      'network',
      'rate limit',
      'service unavailable',
      'temporary',
      'retry'
    ];

    const retryableCodes = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'RATE_LIMIT_EXCEEDED'
    ];

    return retryablePatterns.some(pattern => message.includes(pattern)) ||
           retryableCodes.includes(code);
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserMessage(error: Error, category: ErrorCategory): string {
    switch (category) {
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.';
      
      case 'authentication':
        return 'Authentication failed. Please check your API key and try again.';
      
      case 'validation':
        return 'Invalid input provided. Please check your input and try again.';
      
      case 'permission':
        return 'Permission denied. You may not have access to this resource.';
      
      case 'resource':
        return 'Resource not found or unavailable. Please try again later.';
      
      case 'system':
        return 'System error occurred. Please try again or contact support if the issue persists.';
      
      case 'user':
        return 'Please check your input and try again.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Generate technical error message
   */
  private generateTechnicalMessage(error: Error, category: ErrorCategory): string {
    return `[${category.toUpperCase()}] ${error.name}: ${error.message}`;
  }

  /**
   * Log error to internal storage
   */
  private logError(error: StructuredError): void {
    this.errorLog.push(error);
    
    // Maintain log size limit
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }

    // Log to console based on severity
    const logMethod = this.getLogMethod(error.severity);
    logMethod(`[${error.id}] ${error.technicalMessage}`, {
      category: error.category,
      severity: error.severity,
      context: error.context,
      stack: error.stack
    });
  }

  /**
   * Get appropriate console log method
   */
  private getLogMethod(severity: ErrorSeverity): typeof console.log {
    switch (severity) {
      case 'critical':
      case 'high':
        return console.error;
      case 'medium':
        return console.warn;
      case 'low':
      default:
        return console.log;
    }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): StructuredError[] {
    return this.errorLog.slice(-limit);
  }

  /**
   * Get errors by category
   */
  getErrorsByCategory(category: ErrorCategory): StructuredError[] {
    return this.errorLog.filter(error => error.category === category);
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): StructuredError[] {
    return this.errorLog.filter(error => error.severity === severity);
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: Error;

  for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if error is retryable
      const isRetryable = retryConfig.retryableErrors.some(retryableError =>
        lastError.message.includes(retryableError) ||
        (lastError as any).code === retryableError
      );

      if (!isRetryable || attempt === retryConfig.maxAttempts) {
        throw lastError;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        retryConfig.baseDelay * Math.pow(retryConfig.backoffFactor, attempt - 1),
        retryConfig.maxDelay
      );

      // Add jitter to prevent thundering herd
      const jitteredDelay = delay + Math.random() * 1000;

      await new Promise(resolve => setTimeout(resolve, jitteredDelay));
    }
  }

  throw lastError!;
}

/**
 * Circuit breaker implementation
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = 'closed';
  }
}

/**
 * Global error handler instance
 */
export const errorHandler = new ErrorHandler();

/**
 * Global error handler for unhandled errors
 */
export function setupGlobalErrorHandling(): void {
  process.on('uncaughtException', (error) => {
    const structuredError = errorHandler.processError(error, {
      operation: 'uncaught_exception',
      component: 'global'
    });
    
    console.error('Uncaught Exception:', structuredError);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    const structuredError = errorHandler.processError(reason, {
      operation: 'unhandled_rejection',
      component: 'global'
    });
    
    console.error('Unhandled Rejection at:', promise, 'reason:', structuredError);
  });
}
