import { AppConfig } from '../types/index.js';
/**
 * Load configuration from multiple sources with priority:
 * 1. Command line arguments (highest priority)
 * 2. Environment variables
 * 3. Project configuration (./.kritrima-ai/config.json)
 * 4. User configuration (~/.kritrima-ai/config.json)
 * 5. Default values (lowest priority)
 */
export declare function loadConfig(overrides?: Partial<AppConfig>): AppConfig;
/**
 * Save configuration to user config file
 */
export declare function saveUserConfig(config: Partial<AppConfig>): void;
/**
 * Save configuration to project config file
 */
export declare function saveProjectConfig(config: Partial<AppConfig>): void;
/**
 * Get user configuration directory path
 */
export declare function getUserConfigPath(): string;
/**
 * Get project configuration directory path
 */
export declare function getProjectConfigPath(): string;
/**
 * Get base URL for a provider with environment variable override support
 */
export declare function getBaseUrl(provider?: string): string;
/**
 * Get API key for a provider with fallback mechanisms
 */
export declare function getApiKey(provider?: string): string | undefined;
/**
 * Discover project documentation path
 */
export declare function discoverProjectDocPath(startDir?: string): string | null;
/**
 * Check if we're in a git repository
 */
export declare function isInGitRepository(dir?: string): boolean;
/**
 * Get git repository root
 */
export declare function getGitRepositoryRoot(dir?: string): string | null;
//# sourceMappingURL=config.d.ts.map