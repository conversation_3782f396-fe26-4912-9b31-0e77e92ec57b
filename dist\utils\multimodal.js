import * as fs from 'fs-extra';
import * as path from 'path';
import * as mime from 'mime-types';
/**
 * Supported image formats for multimodal AI
 */
export const SUPPORTED_IMAGE_FORMATS = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff'
];
/**
 * Supported document formats for analysis
 */
export const SUPPORTED_DOCUMENT_FORMATS = [
    'text/plain',
    'text/markdown',
    'application/json',
    'application/xml',
    'text/csv',
    'application/pdf'
];
/**
 * Maximum file size for uploads (10MB)
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024;
/**
 * Process file for multimodal AI input
 */
export async function processFileForAI(filePath, config) {
    try {
        // Resolve and validate file path
        const resolvedPath = path.resolve(filePath);
        // Security check - ensure file is within allowed directories
        const workdir = path.resolve(config.workdir || process.cwd());
        const additionalRoots = (config.additionalWritableRoots || []).map(root => path.resolve(root));
        const allowedPaths = [workdir, ...additionalRoots];
        const isAllowed = allowedPaths.some(allowedPath => resolvedPath.startsWith(allowedPath));
        if (!isAllowed) {
            return {
                success: false,
                error: 'File is outside allowed directories'
            };
        }
        // Check if file exists
        if (!await fs.pathExists(resolvedPath)) {
            return {
                success: false,
                error: 'File does not exist'
            };
        }
        // Get file stats
        const stats = await fs.stat(resolvedPath);
        // Check file size
        if (stats.size > MAX_FILE_SIZE) {
            return {
                success: false,
                error: `File too large. Maximum size is ${Math.round(MAX_FILE_SIZE / 1024 / 1024)}MB`
            };
        }
        // Determine MIME type
        const mimeType = mime.lookup(resolvedPath) || 'application/octet-stream';
        // Check if file type is supported
        const isImage = SUPPORTED_IMAGE_FORMATS.includes(mimeType);
        const isDocument = SUPPORTED_DOCUMENT_FORMATS.includes(mimeType);
        if (!isImage && !isDocument) {
            return {
                success: false,
                error: `Unsupported file type: ${mimeType}`
            };
        }
        // Read file content
        if (isImage) {
            // For images, encode as base64
            const buffer = await fs.readFile(resolvedPath);
            const base64Data = buffer.toString('base64');
            const dataUrl = `data:${mimeType};base64,${base64Data}`;
            return {
                success: true,
                data: dataUrl,
                mimeType,
                size: stats.size
            };
        }
        else {
            // For documents, read as text
            const content = await fs.readFile(resolvedPath, 'utf-8');
            return {
                success: true,
                data: content,
                mimeType,
                size: stats.size
            };
        }
    }
    catch (error) {
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error processing file'
        };
    }
}
/**
 * Create multimodal content from file
 */
export async function createMultimodalContent(filePath, config, detail = 'auto') {
    const result = await processFileForAI(filePath, config);
    if (!result.success || !result.data || !result.mimeType) {
        return null;
    }
    const isImage = SUPPORTED_IMAGE_FORMATS.includes(result.mimeType);
    if (isImage) {
        return {
            type: 'image_url',
            image_url: {
                url: result.data,
                detail
            }
        };
    }
    else {
        return {
            type: 'document',
            document: {
                content: result.data,
                filename: path.basename(filePath),
                mimeType: result.mimeType
            }
        };
    }
}
/**
 * Extract file references from user input
 */
export function extractFileReferences(input) {
    // Match @filename patterns
    const filePattern = /@([^\s]+)/g;
    const matches = input.match(filePattern);
    if (!matches)
        return [];
    return matches.map(match => match.substring(1)); // Remove @ prefix
}
/**
 * Process user input with file references
 */
export async function processInputWithFiles(input, config) {
    const fileRefs = extractFileReferences(input);
    const multimodalContent = [];
    const errors = [];
    // Process each file reference
    for (const fileRef of fileRefs) {
        const content = await createMultimodalContent(fileRef, config);
        if (content) {
            multimodalContent.push(content);
        }
        else {
            errors.push(`Failed to process file: ${fileRef}`);
        }
    }
    // Remove file references from text and add file context
    let processedText = input;
    for (const fileRef of fileRefs) {
        processedText = processedText.replace(new RegExp(`@${fileRef}`, 'g'), '');
    }
    // Add file context to text
    if (multimodalContent.length > 0) {
        const fileContext = multimodalContent
            .filter(content => content.type === 'document')
            .map(content => `File: ${content.document?.filename}\nContent:\n${content.document?.content}`)
            .join('\n\n');
        if (fileContext) {
            processedText = `${processedText}\n\nReferenced files:\n${fileContext}`;
        }
    }
    return {
        text: processedText.trim(),
        multimodalContent,
        errors
    };
}
/**
 * Validate file for AI processing
 */
export async function validateFileForAI(filePath) {
    try {
        if (!await fs.pathExists(filePath)) {
            return { valid: false, error: 'File does not exist' };
        }
        const stats = await fs.stat(filePath);
        const mimeType = mime.lookup(filePath) || 'application/octet-stream';
        if (stats.size > MAX_FILE_SIZE) {
            return {
                valid: false,
                error: `File too large (${Math.round(stats.size / 1024 / 1024)}MB). Maximum size is ${Math.round(MAX_FILE_SIZE / 1024 / 1024)}MB`,
                mimeType,
                size: stats.size
            };
        }
        const isSupported = SUPPORTED_IMAGE_FORMATS.includes(mimeType) ||
            SUPPORTED_DOCUMENT_FORMATS.includes(mimeType);
        if (!isSupported) {
            return {
                valid: false,
                error: `Unsupported file type: ${mimeType}`,
                mimeType,
                size: stats.size
            };
        }
        return {
            valid: true,
            mimeType,
            size: stats.size
        };
    }
    catch (error) {
        return {
            valid: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * Get file type description for user
 */
export function getFileTypeDescription(mimeType) {
    const typeMap = {
        'image/jpeg': 'JPEG Image',
        'image/jpg': 'JPEG Image',
        'image/png': 'PNG Image',
        'image/gif': 'GIF Image',
        'image/webp': 'WebP Image',
        'image/bmp': 'BMP Image',
        'image/tiff': 'TIFF Image',
        'text/plain': 'Text File',
        'text/markdown': 'Markdown File',
        'application/json': 'JSON File',
        'application/xml': 'XML File',
        'text/csv': 'CSV File',
        'application/pdf': 'PDF Document'
    };
    return typeMap[mimeType] || mimeType;
}
/**
 * Format file size for display
 */
export function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
}
//# sourceMappingURL=multimodal.js.map