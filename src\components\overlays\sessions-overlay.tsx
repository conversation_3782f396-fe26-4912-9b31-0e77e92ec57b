import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Session } from '../../types';

interface SessionsOverlayProps {
  onSessionLoad: (session: Session) => void;
  onSessionSave: (name: string) => void;
  onSessionDelete: (sessionId: string) => void;
  onClose: () => void;
}

type TabType = 'load' | 'save' | 'manage';

export function SessionsOverlay({ onSessionLoad, onSessionSave, onSessionDelete, onClose }: SessionsOverlayProps) {
  const [activeTab, setActiveTab] = useState<TabType>('load');
  const [sessions, setSessions] = useState<Session[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [saveSessionName, setSaveSessionName] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load sessions on mount
  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const loadedSessions = await loadSessionsFromStorage();
      setSessions(loadedSessions);
      setSelectedIndex(0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  useInput((input, key) => {
    if (key.escape) {
      onClose();
      return;
    }

    if (key.tab) {
      const tabs: TabType[] = ['load', 'save', 'manage'];
      const currentIndex = tabs.indexOf(activeTab);
      const nextIndex = (currentIndex + 1) % tabs.length;
      setActiveTab(tabs[nextIndex]);
      return;
    }

    if (activeTab === 'save') {
      if (key.return) {
        if (saveSessionName.trim()) {
          onSessionSave(saveSessionName.trim());
          onClose();
        }
        return;
      }

      if (key.backspace) {
        setSaveSessionName(prev => prev.slice(0, -1));
        return;
      }

      if (input && !key.ctrl && !key.meta) {
        setSaveSessionName(prev => prev + input);
        return;
      }
    } else {
      if (key.return) {
        if (activeTab === 'load' && sessions[selectedIndex]) {
          onSessionLoad(sessions[selectedIndex]);
          onClose();
        } else if (activeTab === 'manage' && sessions[selectedIndex]) {
          // Delete session
          onSessionDelete(sessions[selectedIndex].id);
          loadSessions();
        }
        return;
      }

      if (key.upArrow) {
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : sessions.length - 1
        );
        return;
      }

      if (key.downArrow) {
        setSelectedIndex(prev => 
          prev < sessions.length - 1 ? prev + 1 : 0
        );
        return;
      }
    }
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (created: number, updated: number) => {
    const duration = updated - created;
    const minutes = Math.floor(duration / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  };

  return (
    <Box flexDirection="column" height="100%" padding={2}>
      {/* Header */}
      <Box marginBottom={2}>
        <Text color="cyan" bold>
          💾 Session Management
        </Text>
      </Box>

      {/* Tabs */}
      <Box marginBottom={2}>
        <Box marginRight={4}>
          <Text color={activeTab === 'load' ? 'cyan' : 'gray'} bold>
            {activeTab === 'load' ? '▶ ' : '  '}Load
          </Text>
        </Box>
        <Box marginRight={4}>
          <Text color={activeTab === 'save' ? 'cyan' : 'gray'} bold>
            {activeTab === 'save' ? '▶ ' : '  '}Save
          </Text>
        </Box>
        <Box>
          <Text color={activeTab === 'manage' ? 'cyan' : 'gray'} bold>
            {activeTab === 'manage' ? '▶ ' : '  '}Manage
          </Text>
        </Box>
      </Box>

      {/* Content */}
      <Box flexDirection="column" flexGrow={1}>
        {loading && (
          <Text color="blue">Loading sessions...</Text>
        )}

        {error && (
          <Text color="red">❌ {error}</Text>
        )}

        {!loading && !error && activeTab === 'save' && (
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="yellow" bold>
                Save Current Session:
              </Text>
            </Box>
            <Box marginBottom={2}>
              <Text color="green">Name: </Text>
              <Text>{saveSessionName}</Text>
              <Text color="gray">_</Text>
            </Box>
            <Text color="gray">
              Type a name for your session and press Enter to save.
            </Text>
          </Box>
        )}

        {!loading && !error && (activeTab === 'load' || activeTab === 'manage') && (
          <Box flexDirection="column">
            <Box marginBottom={1}>
              <Text color="yellow" bold>
                {activeTab === 'load' ? 'Available Sessions:' : 'Manage Sessions:'}
              </Text>
            </Box>
            
            {sessions.length === 0 ? (
              <Text color="gray">No saved sessions found.</Text>
            ) : (
              sessions.map((session, index) => (
                <Box key={session.id} flexDirection="column" marginBottom={1}>
                  <Box>
                    <Text color={
                      index === selectedIndex 
                        ? (activeTab === 'load' ? 'cyan' : activeTab === 'manage' ? 'red' : 'blue')
                        : 'gray'
                    }>
                      {index === selectedIndex ? '▶ ' : '  '}
                      {session.name}
                      {activeTab === 'manage' && index === selectedIndex && ' [DELETE]'}
                    </Text>
                  </Box>
                  <Box paddingLeft={3}>
                    <Text color="gray" dimColor>
                      Created: {formatDate(session.created)} | 
                      Updated: {formatDate(session.updated)} | 
                      Duration: {formatDuration(session.created, session.updated)} | 
                      Messages: {session.items.length}
                    </Text>
                  </Box>
                  {session.config.model && (
                    <Box paddingLeft={3}>
                      <Text color="gray" dimColor>
                        Model: {session.config.model} ({session.config.provider})
                      </Text>
                    </Box>
                  )}
                </Box>
              ))
            )}
          </Box>
        )}
      </Box>

      {/* Footer */}
      <Box marginTop={2} flexDirection="column">
        <Text color="gray">
          Use ↑↓ to navigate, Tab to switch tabs, Enter to select, Esc to close
        </Text>
        {activeTab === 'save' && (
          <Text color="gray" dimColor>
            Type session name and press Enter to save
          </Text>
        )}
        {activeTab === 'manage' && (
          <Text color="red" dimColor>
            Press Enter to DELETE selected session (cannot be undone)
          </Text>
        )}
      </Box>
    </Box>
  );
}

/**
 * Load sessions from storage
 */
async function loadSessionsFromStorage(): Promise<Session[]> {
  try {
    const fs = require('fs-extra');
    const path = require('path');
    const os = require('os');
    
    const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
    
    if (!fs.existsSync(sessionsDir)) {
      return [];
    }
    
    const files = fs.readdirSync(sessionsDir);
    const sessions: Session[] = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(sessionsDir, file);
          const sessionData = fs.readJsonSync(filePath);
          sessions.push(sessionData);
        } catch (error) {
          console.warn(`Failed to load session ${file}:`, error);
        }
      }
    }
    
    // Sort by updated time (most recent first)
    return sessions.sort((a, b) => b.updated - a.updated);
  } catch (error) {
    throw new Error(`Failed to load sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Save session to storage
 */
export async function saveSessionToStorage(session: Session): Promise<void> {
  try {
    const fs = require('fs-extra');
    const path = require('path');
    const os = require('os');
    
    const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
    await fs.ensureDir(sessionsDir);
    
    const fileName = `${session.id}.json`;
    const filePath = path.join(sessionsDir, fileName);
    
    await fs.writeJson(filePath, session, { spaces: 2 });
  } catch (error) {
    throw new Error(`Failed to save session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete session from storage
 */
export async function deleteSessionFromStorage(sessionId: string): Promise<void> {
  try {
    const fs = require('fs-extra');
    const path = require('path');
    const os = require('os');
    
    const sessionsDir = path.join(os.homedir(), '.kritrima-ai', 'sessions');
    const fileName = `${sessionId}.json`;
    const filePath = path.join(sessionsDir, fileName);
    
    if (fs.existsSync(filePath)) {
      await fs.remove(filePath);
    }
  } catch (error) {
    throw new Error(`Failed to delete session: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
