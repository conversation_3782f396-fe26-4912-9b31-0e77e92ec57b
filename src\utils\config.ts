import * as fs from 'fs-extra';
import { existsSync, readFileSync } from 'fs';
import * as path from 'path';
import * as yaml from 'yaml';
import { AppConfig, ConfigError } from '../types/index.js';
import { getProvider } from './providers.js';

const DEFAULT_CONFIG: AppConfig = {
  model: "gpt-4",
  provider: "openai",
  approvalMode: "suggest",
  maxTokens: 4096,
  temperature: 0.7,
  timeout: 30000,
  workdir: undefined, // Will be set to process.cwd() during loading
  additionalWritableRoots: []
};

/**
 * Filter out empty values from config object
 */
function filterEmptyValues(config: Partial<AppConfig>): Partial<AppConfig> {
  const filtered: Partial<AppConfig> = {};

  for (const [key, value] of Object.entries(config)) {
    if (value !== '' && value !== null && value !== undefined) {
      (filtered as any)[key] = value;
    }
  }

  return filtered;
}

/**
 * Load configuration from multiple sources with priority:
 * 1. Command line arguments (highest priority)
 * 2. Environment variables
 * 3. Project configuration (./.kritrima-ai/config.json)
 * 4. User configuration (~/.kritrima-ai/config.json)
 * 5. Default values (lowest priority)
 */
export function loadConfig(overrides: Partial<AppConfig> = {}): AppConfig {
  const config = { ...DEFAULT_CONFIG };

  // Load user config
  const userConfig = loadUserConfig();
  Object.assign(config, filterEmptyValues(userConfig));

  // Load project config
  const projectConfig = loadProjectConfig();
  Object.assign(config, filterEmptyValues(projectConfig));

  // Apply environment variables
  applyEnvironmentVariables(config);

  // Apply overrides (command line arguments)
  Object.assign(config, overrides);

  // Set default workdir if not specified
  if (!config.workdir) {
    config.workdir = process.cwd();
  }

  // Validate and normalize
  validateConfig(config);

  return config;
}

/**
 * Load user configuration from ~/.kritrima-ai/config.json
 */
function loadUserConfig(): Partial<AppConfig> {
  const userConfigPath = getUserConfigPath();
  return loadConfigFile(userConfigPath);
}

/**
 * Load project configuration from ./.kritrima-ai/config.json
 */
function loadProjectConfig(): Partial<AppConfig> {
  const projectConfigPath = getProjectConfigPath();
  return loadConfigFile(projectConfigPath);
}

/**
 * Load configuration from a file (supports JSON and YAML)
 */
function loadConfigFile(filePath: string): Partial<AppConfig> {
  if (!existsSync(filePath)) {
    return {};
  }

  try {
    const content = readFileSync(filePath, 'utf-8');
    const ext = path.extname(filePath).toLowerCase();

    if (ext === '.yaml' || ext === '.yml') {
      return yaml.parse(content) || {};
    } else {
      return JSON.parse(content) || {};
    }
  } catch (error) {
    console.warn(`Warning: Failed to load config from ${filePath}:`, error);
    return {};
  }
}

/**
 * Apply environment variables to configuration
 */
function applyEnvironmentVariables(config: AppConfig): void {
  if (process.env.KRITRIMA_AI_MODEL) {
    config.model = process.env.KRITRIMA_AI_MODEL;
  }

  if (process.env.KRITRIMA_AI_PROVIDER) {
    config.provider = process.env.KRITRIMA_AI_PROVIDER;
  }

  if (process.env.KRITRIMA_AI_APPROVAL_MODE) {
    config.approvalMode = process.env.KRITRIMA_AI_APPROVAL_MODE as any;
  }

  if (process.env.KRITRIMA_AI_MAX_TOKENS) {
    config.maxTokens = parseInt(process.env.KRITRIMA_AI_MAX_TOKENS, 10);
  }

  if (process.env.KRITRIMA_AI_TEMPERATURE) {
    config.temperature = parseFloat(process.env.KRITRIMA_AI_TEMPERATURE);
  }

  if (process.env.KRITRIMA_AI_TIMEOUT) {
    config.timeout = parseInt(process.env.KRITRIMA_AI_TIMEOUT, 10);
  }

  if (process.env.KRITRIMA_AI_WORKDIR) {
    config.workdir = process.env.KRITRIMA_AI_WORKDIR;
  }
}

/**
 * Validate configuration
 */
function validateConfig(config: AppConfig): void {
  if (!config.model) {
    throw new ConfigError("Model is required");
  }

  if (!config.provider) {
    throw new ConfigError("Provider is required");
  }

  if (!["suggest", "auto-edit", "full-auto"].includes(config.approvalMode)) {
    throw new ConfigError("Invalid approval mode");
  }

  if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 128000)) {
    throw new ConfigError("Max tokens must be between 1 and 128000");
  }

  if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
    throw new ConfigError("Temperature must be between 0 and 2");
  }

  if (config.timeout && config.timeout < 1000) {
    throw new ConfigError("Timeout must be at least 1000ms");
  }
}

/**
 * Save configuration to user config file
 */
export function saveUserConfig(config: Partial<AppConfig>): void {
  const userConfigPath = getUserConfigPath();
  const userConfigDir = path.dirname(userConfigPath);

  fs.ensureDirSync(userConfigDir);
  fs.writeFileSync(userConfigPath, JSON.stringify(config, null, 2));
}

/**
 * Save configuration to project config file
 */
export function saveProjectConfig(config: Partial<AppConfig>): void {
  const projectConfigPath = getProjectConfigPath();
  const projectConfigDir = path.dirname(projectConfigPath);

  fs.ensureDirSync(projectConfigDir);
  fs.writeFileSync(projectConfigPath, JSON.stringify(config, null, 2));
}

/**
 * Get user configuration directory path
 */
export function getUserConfigPath(): string {
  const homeDir = process.env.HOME || process.env.USERPROFILE || '';
  return path.join(homeDir, '.kritrima-ai', 'config.json');
}

/**
 * Get project configuration directory path
 */
export function getProjectConfigPath(): string {
  return path.join(process.cwd(), '.kritrima-ai', 'config.json');
}

/**
 * Get base URL for a provider with environment variable override support
 */
export function getBaseUrl(provider: string = "openai"): string {
  const config = loadConfig();
  const providerInfo = config.providers?.[provider.toLowerCase()] || getProvider(provider);

  if (!providerInfo) {
    throw new ConfigError(`Unknown provider: ${provider}`);
  }

  // Check for environment variable override
  const envOverride = process.env[`${provider.toUpperCase()}_BASE_URL`];
  if (envOverride) {
    return envOverride;
  }

  return providerInfo.baseURL;
}

/**
 * Get API key for a provider with fallback mechanisms
 */
export function getApiKey(provider: string = "openai"): string | undefined {
  const config = loadConfig();
  const providerInfo = config.providers?.[provider.toLowerCase()] || getProvider(provider);

  if (providerInfo) {
    // Try provider-specific environment variable
    const apiKey = process.env[providerInfo.envKey];
    if (apiKey) return apiKey;
  }

  // Try generic environment variable
  const genericKey = process.env[`${provider.toUpperCase()}_API_KEY`];
  if (genericKey) return genericKey;

  // Try common fallbacks
  if (provider.toLowerCase() === 'openai') {
    return process.env.OPENAI_API_KEY;
  }

  return undefined;
}

/**
 * Discover project documentation path
 */
export function discoverProjectDocPath(startDir: string = process.cwd()): string | null {
  const candidates = [
    "AGENTS.md",
    "README.md",
    "docs/README.md",
    "docs/AGENTS.md",
    ".kritrima-ai/README.md",
    ".kritrima-ai/AGENTS.md"
  ];

  let currentDir = startDir;

  // Search upward through directory tree
  while (currentDir !== path.dirname(currentDir)) {
    for (const candidate of candidates) {
      const fullPath = path.join(currentDir, candidate);
      if (existsSync(fullPath)) {
        return fullPath;
      }
    }
    currentDir = path.dirname(currentDir);
  }

  return null;
}

/**
 * Check if we're in a git repository
 */
export function isInGitRepository(dir: string = process.cwd()): boolean {
  let currentDir = dir;

  while (currentDir !== path.dirname(currentDir)) {
    if (existsSync(path.join(currentDir, '.git'))) {
      return true;
    }
    currentDir = path.dirname(currentDir);
  }

  return false;
}

/**
 * Get git repository root
 */
export function getGitRepositoryRoot(dir: string = process.cwd()): string | null {
  let currentDir = dir;

  while (currentDir !== path.dirname(currentDir)) {
    if (existsSync(path.join(currentDir, '.git'))) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }

  return null;
}
