{"version": 3, "file": "approval-mode-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/approval-mode-overlay.tsx"], "names": [], "mappings": ";AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACjC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAS1C,MAAM,cAAc,GAOf;IACH;QACE,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,2CAA2C;QACxD,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,IAAI;QACV,OAAO,EAAE;YACP,0CAA0C;YAC1C,gCAAgC;YAChC,mCAAmC;YACnC,sCAAsC;SACvC;KACF;IACD;QACE,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,oDAAoD;QACjE,KAAK,EAAE,QAAQ;QACf,IAAI,EAAE,GAAG;QACT,OAAO,EAAE;YACP,yCAAyC;YACzC,2DAA2D;YAC3D,qCAAqC;YACrC,qCAAqC;SACtC;KACF;IACD;QACE,IAAI,EAAE,WAAW;QACjB,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,8CAA8C;QAC3D,KAAK,EAAE,OAAO;QACd,IAAI,EAAE,IAAI;QACV,OAAO,EAAE;YACP,uCAAuC;YACvC,iCAAiC;YACjC,gDAAgD;YAChD,8CAA8C;SAC/C;KACF;CACF,CAAC;AAEF,MAAM,UAAU,mBAAmB,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAA4B;IAClG,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAChD,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAC5D,CAAC;IAEF,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,YAAY,EAAE,CAAC;gBACjB,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CACtB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAChD,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CACtB,IAAI,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAChD,CAAC;YACF,OAAO;QACT,CAAC;QAED,4BAA4B;QAC5B,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC7C,gBAAgB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC1B,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAEnD,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aAElD,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,yDAEhB,GACH,EAGN,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,YACpD,MAAC,IAAI,iCACW,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kBAAE,WAAW,GAAQ,IACrD,GACH,EAGN,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,YACpC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CACnC,MAAC,GAAG,IAAiB,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzD,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAE,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAY,EAAE,IAAI,mBACpE,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACrC,IAAI,CAAC,IAAI,OAAG,IAAI,CAAC,IAAI,EACrB,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,YAAY,IACrC,GACH,EAEN,KAAC,GAAG,IAAC,WAAW,EAAE,CAAC,YACjB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YACf,IAAI,CAAC,WAAW,GACZ,GACH,EAEL,KAAK,KAAK,aAAa,IAAI,CAC1B,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC,CACzC,KAAC,IAAI,IAAmB,KAAK,EAAC,MAAM,EAAC,QAAQ,kBAC1C,MAAM,IADE,WAAW,CAEf,CACR,CAAC,GACE,CACP,KAvBO,IAAI,CAAC,IAAI,CAwBb,CACP,CAAC,GACE,EAGL,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,WAAW,IAAI,CACpD,MAAC,GAAG,IAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,aAC/C,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,EAAC,IAAI,4DAEf,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,4HAGV,IACH,CACP,EAGD,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACvC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,6EAEX,EACP,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,4CACH,cAAc,CAAC,MAAM,4BACtC,IACH,IACF,CACP,CAAC;AACJ,CAAC"}