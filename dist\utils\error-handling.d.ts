/**
 * Error severity levels
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
/**
 * Error categories
 */
export type ErrorCategory = 'network' | 'authentication' | 'validation' | 'permission' | 'resource' | 'system' | 'user' | 'unknown';
/**
 * Error context information
 */
export interface ErrorContext {
    operation: string;
    component: string;
    userId?: string;
    sessionId?: string;
    timestamp: number;
    metadata?: Record<string, any>;
}
/**
 * Structured error information
 */
export interface StructuredError {
    id: string;
    message: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    context: ErrorContext;
    originalError?: Error;
    stack?: string;
    recoverable: boolean;
    retryable: boolean;
    userMessage: string;
    technicalMessage: string;
}
/**
 * Retry configuration
 */
export interface RetryConfig {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
    retryableErrors: string[];
}
/**
 * Default retry configuration
 */
export declare const DEFAULT_RETRY_CONFIG: RetryConfig;
/**
 * Error handler class
 */
export declare class ErrorHandler {
    private errorLog;
    private maxLogSize;
    /**
     * Process and structure an error
     */
    processError(error: Error | unknown, context: Partial<ErrorContext>): StructuredError;
    /**
     * Categorize error type
     */
    private categorizeError;
    /**
     * Assess error severity
     */
    private assessSeverity;
    /**
     * Check if error is recoverable
     */
    private isRecoverable;
    /**
     * Check if error is retryable
     */
    private isRetryable;
    /**
     * Generate user-friendly error message
     */
    private generateUserMessage;
    /**
     * Generate technical error message
     */
    private generateTechnicalMessage;
    /**
     * Log error to internal storage
     */
    private logError;
    /**
     * Get appropriate console log method
     */
    private getLogMethod;
    /**
     * Get recent errors
     */
    getRecentErrors(limit?: number): StructuredError[];
    /**
     * Get errors by category
     */
    getErrorsByCategory(category: ErrorCategory): StructuredError[];
    /**
     * Get errors by severity
     */
    getErrorsBySeverity(severity: ErrorSeverity): StructuredError[];
    /**
     * Clear error log
     */
    clearErrorLog(): void;
}
/**
 * Retry function with exponential backoff
 */
export declare function retryWithBackoff<T>(operation: () => Promise<T>, config?: Partial<RetryConfig>): Promise<T>;
/**
 * Circuit breaker implementation
 */
export declare class CircuitBreaker {
    private failureThreshold;
    private recoveryTimeout;
    private failures;
    private lastFailureTime;
    private state;
    constructor(failureThreshold?: number, recoveryTimeout?: number);
    execute<T>(operation: () => Promise<T>): Promise<T>;
    private onSuccess;
    private onFailure;
    getState(): string;
    reset(): void;
}
/**
 * Global error handler instance
 */
export declare const errorHandler: ErrorHandler;
/**
 * Global error handler for unhandled errors
 */
export declare function setupGlobalErrorHandling(): void;
//# sourceMappingURL=error-handling.d.ts.map