{"name": "kritrima-ai", "version": "1.0.0", "description": "Sophisticated AI-powered command-line interface with multi-provider support and autonomous agent capabilities", "type": "module", "main": "dist/cli.js", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "scripts": {"build": "tsc --noEmit false --skipLib<PERSON><PERSON><PERSON> true", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "jest", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}", "prepare": "npm run build"}, "keywords": ["ai", "cli", "assistant", "openai", "agent", "automation", "coding"], "author": "Kritrima AI", "license": "MIT", "dependencies": {"ansi-escapes": "^6.2.0", "chalk": "^5.3.0", "clipboardy": "^4.0.0", "commander": "^11.1.0", "diff": "^5.1.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "glob": "^10.3.10", "highlight.js": "^11.9.0", "https-proxy-agent": "^7.0.2", "ink": "^4.4.1", "ink-box": "^2.0.0", "ink-select-input": "^5.0.0", "ink-spinner": "^5.0.0", "ink-text-input": "^5.0.1", "marked": "^9.1.6", "marked-terminal": "^6.2.0", "mime-types": "^2.1.35", "node-fetch": "^3.3.2", "node-notifier": "^10.0.1", "openai": "^4.20.0", "react": "^18.2.0", "react-dom": "^18.2.0", "semver": "^7.5.4", "strip-ansi": "^7.1.0", "terminal-kit": "^3.0.1", "uuid": "^11.1.0", "which": "^4.0.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/diff": "^5.0.8", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.23", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/semver": "^7.5.6", "@types/uuid": "^10.0.0", "@types/which": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/kritrima-ai/kritrima-ai-cli.git"}, "bugs": {"url": "https://github.com/kritrima-ai/kritrima-ai-cli/issues"}, "homepage": "https://github.com/kritrima-ai/kritrima-ai-cli#readme"}