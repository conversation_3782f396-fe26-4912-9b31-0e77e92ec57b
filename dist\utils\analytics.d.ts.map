{"version": 3, "file": "analytics.d.ts", "sourceRoot": "", "sources": ["../../src/utils/analytics.ts"], "names": [], "mappings": "AAIA;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAC1B,WAAW,GACX,UAAU,GACV,cAAc,GACd,kBAAkB,GAClB,kBAAkB,GAClB,gBAAgB,GAChB,gBAAgB,GAChB,mBAAmB,GACnB,gBAAgB,GAChB,iBAAiB,GACjB,gBAAgB,GAChB,oBAAoB,GACpB,kBAAkB,GAClB,iBAAiB,CAAC;AAEtB;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,kBAAkB,CAAC;IACzB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC1B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,WAAW,EAAE,MAAM,CAAC;IACpB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,aAAa,EAAE,MAAM,CAAC;IACtB,gBAAgB,EAAE,MAAM,CAAC;IACzB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,OAAO,EAAE,OAAO,CAAC;IACjB,yBAAyB,EAAE,OAAO,CAAC;IACnC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,OAAO,CAAC;CACpB;AAcD;;GAEG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAAkB;IAChC,OAAO,CAAC,MAAM,CAAwB;IACtC,OAAO,CAAC,aAAa,CAA4B;IACjD,OAAO,CAAC,OAAO,CAAS;IACxB,OAAO,CAAC,UAAU,CAAC,CAAiB;gBAExB,MAAM,GAAE,OAAO,CAAC,eAAe,CAAM;IASjD;;OAEG;YACW,UAAU;IAsBxB;;OAEG;IACH,UAAU,CACR,IAAI,EAAE,kBAAkB,EACxB,IAAI,GAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,EAC9B,OAAO,GAAE;QACP,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,OAAO,CAAC,EAAE,OAAO,CAAC;KACd,GACL,IAAI;IAsBP;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI;IAcnD;;OAEG;IACH,aAAa,IAAI,UAAU;IA4C3B;;OAEG;IACH,qBAAqB,IAAI;QACvB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,eAAe,EAAE,MAAM,CAAC;QACxB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,kBAAkB,EAAE,MAAM,CAAC;QAC3B,aAAa,EAAE,MAAM,CAAC;KACvB;IA0BD;;OAEG;IACG,UAAU,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,CAAC;IAuBlE;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAuB5B;;OAEG;IACG,OAAO,CAAC,aAAa,GAAE,MAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBxD;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAe/B;;OAEG;IACH,OAAO,CAAC,eAAe;IAIvB;;OAEG;IACH,OAAO,CAAC,eAAe;IAcvB;;OAEG;IACH,OAAO,CAAC,aAAa;CActB;AAED;;GAEG;AACH,eAAO,MAAM,SAAS,kBAAyB,CAAC;AAEhD;;GAEG;AACH,eAAO,MAAM,YAAY,GAAI,MAAM,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,UAAU,GAAG,SAChB,CAAC;AAEtD,eAAO,MAAM,YAAY,GAAI,MAAM,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,UAAU,GAAG,SACZ,CAAC;AAE1D,eAAO,MAAM,UAAU,GAAI,OAAO,KAAK,EAAE,OAAM,MAAM,CAAC,MAAM,EAAE,GAAG,CAAM,SACI,CAAC;AAE5E,eAAO,MAAM,gBAAgB,GAAI,MAAM,MAAM,EAAE,IAAI,MAAM,EAAE,UAAU,MAAM,SACX,CAAC;AAEjE,eAAO,MAAM,gBAAgB,GAAI,SAAS,kBAAkB,SACvB,CAAC"}