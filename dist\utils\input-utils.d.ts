import { ResponseInputItem, ImageData } from '../types/index.js';
/**
 * Create input item from text and image paths
 */
export declare function createInputItem(text: string, imagePaths?: string[]): Promise<ResponseInputItem>;
/**
 * Process an image file for API consumption
 */
export declare function processImage(imagePath: string): Promise<ImageData>;
/**
 * Validate image file before processing
 */
export declare function validateImageFile(imagePath: string): {
    valid: boolean;
    error?: string;
};
/**
 * Extract file paths from text using @ prefix
 */
export declare function extractFilePaths(text: string): {
    cleanText: string;
    filePaths: string[];
};
/**
 * Get file system suggestions for autocomplete
 */
export declare function getFileSystemSuggestions(input: string, workdir?: string, maxSuggestions?: number): Array<{
    path: string;
    type: 'file' | 'directory';
    exists: boolean;
}>;
/**
 * Format file size for display
 */
export declare function formatFileSize(bytes: number): string;
/**
 * Check if file is an image
 */
export declare function isImageFile(filePath: string): boolean;
/**
 * Get supported image extensions
 */
export declare function getSupportedImageExtensions(): string[];
//# sourceMappingURL=input-utils.d.ts.map