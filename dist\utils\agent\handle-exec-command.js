import * as path from 'path';
import { SecurityError } from '../../types/index.js';
import { execSandboxed, getSandboxCapabilities } from './sandbox/index.js';
import { createApprovalRequest, shouldRequestApproval, validateCommand } from '../../approvals.js';
/**
 * Handle execution of shell commands with approval workflow
 */
export async function handleExecCommand(args, config, approvalPolicy, additionalWritableRoots = [], getCommandConfirmation, signal) {
    const { command, workdir = config.workdir || process.cwd(), timeout = config.timeout || 30000 } = args;
    // Validate command
    if (!command || command.length === 0) {
        throw new SecurityError('Empty command not allowed');
    }
    // Resolve working directory
    const resolvedWorkdir = path.resolve(workdir);
    // Validate command security
    const validation = validateCommand(command, resolvedWorkdir, [...additionalWritableRoots]);
    if (!validation.valid) {
        throw new SecurityError(validation.error || 'Command validation failed');
    }
    // Check if approval is required
    const requiresApproval = shouldRequestApproval(command, approvalPolicy);
    if (requiresApproval && getCommandConfirmation) {
        const approvalRequest = createApprovalRequest(command, resolvedWorkdir);
        const approved = await getCommandConfirmation(approvalRequest);
        if (!approved) {
            return {
                outputText: 'Command execution cancelled by user',
                metadata: {
                    cancelled: true,
                    command: command.join(' '),
                    workdir: resolvedWorkdir
                },
                additionalItems: []
            };
        }
    }
    // Prepare execution input
    const execInput = {
        command,
        workdir: resolvedWorkdir,
        timeout,
        env: {
            // Preserve important environment variables
            PATH: process.env.PATH || '',
            HOME: process.env.HOME || process.env.USERPROFILE || '',
            USER: process.env.USER || process.env.USERNAME || '',
            // Add any custom environment variables from config
            ...config.env
        }
    };
    try {
        // Execute command in sandbox
        const result = await execSandboxed(execInput, config, additionalWritableRoots, signal);
        // Format output
        const outputText = formatExecOutput(result);
        // Prepare metadata
        const metadata = {
            command: result.command.join(' '),
            workdir: result.workdir,
            exitCode: result.exitCode,
            duration: result.duration,
            success: result.success,
            sandbox: getSandboxCapabilities().name,
            timestamp: Date.now()
        };
        // Create additional items for detailed output if needed
        const additionalItems = [];
        // If command produced significant output, create separate items
        if (result.stdout.length > 1000 || result.stderr.length > 100) {
            if (result.stdout) {
                additionalItems.push({
                    role: 'system',
                    content: [{
                            type: 'input_text',
                            text: `Command stdout:\n${result.stdout}`
                        }],
                    type: 'message',
                    timestamp: Date.now()
                });
            }
            if (result.stderr) {
                additionalItems.push({
                    role: 'system',
                    content: [{
                            type: 'input_text',
                            text: `Command stderr:\n${result.stderr}`
                        }],
                    type: 'message',
                    timestamp: Date.now()
                });
            }
        }
        return {
            outputText,
            metadata,
            additionalItems
        };
    }
    catch (error) {
        // Handle execution errors
        const errorMessage = error instanceof Error ? error.message : 'Unknown execution error';
        return {
            outputText: `Command execution failed: ${errorMessage}`,
            metadata: {
                command: command.join(' '),
                workdir: resolvedWorkdir,
                error: errorMessage,
                success: false,
                timestamp: Date.now()
            },
            additionalItems: []
        };
    }
}
/**
 * Format execution result for output
 */
function formatExecOutput(result) {
    const { command, workdir, exitCode, duration, success, stdout, stderr, error } = result;
    let output = `Command: ${command.join(' ')}\n`;
    output += `Working Directory: ${workdir}\n`;
    output += `Exit Code: ${exitCode}\n`;
    output += `Duration: ${duration}ms\n`;
    output += `Status: ${success ? 'SUCCESS' : 'FAILED'}\n`;
    if (stdout) {
        output += `\nStdout:\n${stdout}`;
    }
    if (stderr) {
        output += `\nStderr:\n${stderr}`;
    }
    if (error) {
        output += `\nError: ${error}`;
    }
    return output;
}
/**
 * Parse shell command from string
 */
export function parseShellCommand(commandString) {
    // Simple command parsing - split by spaces but respect quotes
    const args = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    for (let i = 0; i < commandString.length; i++) {
        const char = commandString[i];
        if (!inQuotes && (char === '"' || char === "'")) {
            inQuotes = true;
            quoteChar = char;
        }
        else if (inQuotes && char === quoteChar) {
            inQuotes = false;
            quoteChar = '';
        }
        else if (!inQuotes && char === ' ') {
            if (current) {
                args.push(current);
                current = '';
            }
        }
        else {
            current += char;
        }
    }
    if (current) {
        args.push(current);
    }
    return args;
}
/**
 * Validate working directory
 */
function validateWorkingDirectory(workdir, allowedRoots) {
    const resolvedWorkdir = path.resolve(workdir);
    // Check if workdir is within allowed roots
    const isAllowed = allowedRoots.some(root => {
        const resolvedRoot = path.resolve(root);
        return resolvedWorkdir.startsWith(resolvedRoot);
    });
    return isAllowed;
}
/**
 * Get command execution summary
 */
export function getExecutionSummary(result) {
    const { command, exitCode, duration, success } = result;
    const status = success ? '✓' : '✗';
    const commandStr = command.join(' ');
    return `${status} ${commandStr} (${duration}ms, exit: ${exitCode})`;
}
/**
 * Check if command is likely to produce large output
 */
export function isLargeOutputCommand(command) {
    const largeOutputCommands = [
        'find', 'grep', 'cat', 'type', 'ls', 'dir',
        'git log', 'git diff', 'npm list', 'pip list',
        'ps', 'tasklist', 'netstat', 'lsof'
    ];
    const mainCommand = command[0]?.toLowerCase();
    return largeOutputCommands.includes(mainCommand);
}
/**
 * Suggest command optimizations
 */
export function suggestCommandOptimizations(command) {
    const suggestions = [];
    const commandStr = command.join(' ').toLowerCase();
    // Suggest pagination for commands that might produce large output
    if (isLargeOutputCommand(command)) {
        if (!commandStr.includes('head') && !commandStr.includes('tail') && !commandStr.includes('more')) {
            suggestions.push('Consider using head, tail, or more to limit output');
        }
    }
    // Suggest specific flags for common commands
    if (command[0] === 'ls' && !commandStr.includes('-l')) {
        suggestions.push('Use -l flag for detailed file information');
    }
    if (command[0] === 'git' && command[1] === 'log' && !commandStr.includes('--oneline')) {
        suggestions.push('Use --oneline flag for compact git log output');
    }
    return suggestions;
}
/**
 * Estimate command execution time
 */
export function estimateExecutionTime(command) {
    const fastCommands = ['echo', 'pwd', 'cd', 'which', 'where'];
    const slowCommands = ['find', 'grep', 'git clone', 'npm install', 'pip install'];
    const mainCommand = command[0]?.toLowerCase();
    if (fastCommands.includes(mainCommand)) {
        return 'fast';
    }
    if (slowCommands.includes(mainCommand)) {
        return 'slow';
    }
    return 'medium';
}
/**
 * Get command help text
 */
export function getCommandHelp(command) {
    const helpTexts = {
        'ls': 'List directory contents. Use -l for detailed view, -a to show hidden files.',
        'cat': 'Display file contents. Use with filename to read file.',
        'grep': 'Search for patterns in files. Usage: grep "pattern" filename',
        'find': 'Search for files and directories. Usage: find . -name "pattern"',
        'git': 'Git version control. Common commands: status, add, commit, push, pull',
        'npm': 'Node.js package manager. Common commands: install, start, test, build',
        'pip': 'Python package installer. Common commands: install, list, show',
        'docker': 'Container management. Common commands: run, build, ps, images'
    };
    return helpTexts[command.toLowerCase()] || `No help available for command: ${command}`;
}
//# sourceMappingURL=handle-exec-command.js.map