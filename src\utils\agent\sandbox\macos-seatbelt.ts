import { spawn } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { ExecInput, ExecResult, AppConfig, SecurityError } from '../../../types/index.js';
import { adaptCommand } from '../platform-commands.js';

/**
 * macOS Seatbelt sandbox implementation
 * Provides system-level sandboxing using macOS Seatbelt framework
 */

/**
 * Check if Seatbelt is available on the system
 */
export function isAvailable(): boolean {
  try {
    // Check if we're on macOS
    if (process.platform !== 'darwin') {
      return false;
    }

    // Check if sandbox-exec is available
    return fs.existsSync('/usr/bin/sandbox-exec');
  } catch (error) {
    return false;
  }
}

/**
 * Execute command with Seatbelt sandboxing
 */
export async function execWithSeatbelt(
  input: ExecInput,
  config: AppConfig,
  additionalWritableRoots: ReadonlyArray<string> = [],
  abortSignal?: AbortSignal
): Promise<ExecResult> {
  if (!isAvailable()) {
    throw new SecurityError('Seatbelt sandboxing not available on this system');
  }

  const startTime = Date.now();
  const { command, workdir = config.workdir || process.cwd(), timeout = config.timeout || 30000, env = {} } = input;

  // Validate input
  if (!command || command.length === 0) {
    throw new SecurityError('Empty command not allowed');
  }

  // Adapt command for current platform
  const adaptedCommand = adaptCommand(command);
  
  // Resolve working directory
  const resolvedWorkdir = path.resolve(workdir);
  
  // Create Seatbelt profile
  const seatbeltProfile = createSeatbeltProfile(resolvedWorkdir, additionalWritableRoots);
  
  // Prepare environment
  const execEnv = {
    ...process.env,
    ...env,
    PATH: process.env.PATH || '',
    PWD: resolvedWorkdir
  };

  // Build sandbox-exec command
  const commandString = adaptedCommand.map(arg => `'${arg.replace(/'/g, "'\"'\"'")}'`).join(' ');
  const sandboxArgs = [
    '/usr/bin/sandbox-exec',
    '-p', seatbeltProfile,
    '/bin/bash', '-c', commandString
  ];

  return new Promise<ExecResult>((resolve) => {
    let stdout = '';
    let stderr = '';
    let isResolved = false;

    // Create child process
    const child = spawn(sandboxArgs[0], sandboxArgs.slice(1), {
      cwd: resolvedWorkdir,
      env: execEnv,
      stdio: ['pipe', 'pipe', 'pipe'],
      windowsHide: true
    });

    // Handle abort signal
    const abortHandler = () => {
      if (!isResolved) {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }
    };

    if (abortSignal) {
      abortSignal.addEventListener('abort', abortHandler);
    }

    // Set timeout
    const timeoutId = setTimeout(() => {
      if (!isResolved) {
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }
    }, timeout);

    // Collect stdout
    child.stdout?.on('data', (data: Buffer) => {
      stdout += data.toString();
    });

    // Collect stderr
    child.stderr?.on('data', (data: Buffer) => {
      stderr += data.toString();
    });

    // Handle process exit
    child.on('exit', (code) => {
      if (isResolved) return;
      isResolved = true;

      clearTimeout(timeoutId);
      if (abortSignal) {
        abortSignal.removeEventListener('abort', abortHandler);
      }

      const duration = Date.now() - startTime;
      const exitCode = code || 0;

      const result: ExecResult = {
        success: exitCode === 0,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode,
        duration,
        command: adaptedCommand,
        workdir: resolvedWorkdir
      };

      resolve(result);
    });

    // Handle process errors
    child.on('error', (error) => {
      if (isResolved) return;
      isResolved = true;

      clearTimeout(timeoutId);
      if (abortSignal) {
        abortSignal.removeEventListener('abort', abortHandler);
      }

      const duration = Date.now() - startTime;

      const result: ExecResult = {
        success: false,
        stdout: stdout.trim(),
        stderr: stderr.trim() || error.message,
        exitCode: 1,
        duration,
        command: adaptedCommand,
        workdir: resolvedWorkdir,
        error: error.message
      };

      resolve(result);
    });
  });
}

/**
 * Create a Seatbelt sandbox profile
 */
function createSeatbeltProfile(
  workdir: string,
  additionalWritableRoots: ReadonlyArray<string>
): string {
  const allowedReadPaths = [
    '/System',
    '/usr',
    '/bin',
    '/sbin',
    '/Library/Frameworks',
    '/Library/PrivateFrameworks',
    '/private/var/db/timezone',
    workdir,
    ...additionalWritableRoots
  ];

  const allowedWritePaths = [
    '/tmp',
    '/private/tmp',
    '/var/tmp',
    workdir,
    ...additionalWritableRoots
  ];

  return `
(version 1)

; Default deny
(deny default)

; Allow basic system operations
(allow process-info*)
(allow process-info-pidinfo)
(allow process-info-pidfdinfo)
(allow process-info-pidfileportinfo)
(allow process-info-setcontrol)
(allow process-info-dirtycontrol)
(allow process-info-rusage)

; Allow reading system files
${allowedReadPaths.map(p => `(allow file-read* (subpath "${p}"))`).join('\n')}

; Allow writing to specific paths
${allowedWritePaths.map(p => `(allow file-write* (subpath "${p}"))`).join('\n')}

; Allow reading standard input/output
(allow file-read-data file-write-data
  (literal "/dev/stdin")
  (literal "/dev/stdout")
  (literal "/dev/stderr")
  (literal "/dev/null")
  (literal "/dev/zero")
  (literal "/dev/urandom"))

; Allow basic networking (if needed)
(allow network-outbound
  (remote tcp "*:80")
  (remote tcp "*:443")
  (remote tcp "*:22"))

; Allow process execution for shell commands
(allow process-exec
  (literal "/bin/bash")
  (literal "/bin/sh")
  (literal "/usr/bin/env")
  (subpath "/usr/bin")
  (subpath "/bin"))

; Allow signal operations
(allow signal (target self))

; Allow memory operations
(allow vm-map)
(allow vm-protect)

; Allow thread operations
(allow thread-create)
(allow thread-set-state)

; Deny dangerous operations
(deny file-write* (subpath "/System"))
(deny file-write* (subpath "/usr"))
(deny file-write* (subpath "/bin"))
(deny file-write* (subpath "/sbin"))
(deny process-exec (subpath "/System/Library/CoreServices"))
(deny network-bind)
(deny network-inbound)
`;
}

/**
 * Get Seatbelt capabilities
 */
export function getCapabilities(): {
  sandboxed: boolean;
  networkRestricted: boolean;
  filesystemRestricted: boolean;
  processRestricted: boolean;
} {
  return {
    sandboxed: true,
    networkRestricted: true,
    filesystemRestricted: true,
    processRestricted: true
  };
}

/**
 * Get security level description
 */
export function getSecurityLevel(): string {
  return 'High - System-level sandboxing with macOS Seatbelt';
}

/**
 * Test Seatbelt functionality
 */
export async function testSeatbelt(): Promise<{
  available: boolean;
  version?: string;
  features?: string[];
  error?: string;
}> {
  try {
    if (!isAvailable()) {
      return {
        available: false,
        error: 'Seatbelt not available on this system'
      };
    }

    // Test basic functionality
    const testResult = await execWithSeatbelt(
      { command: ['echo', 'test'] },
      { workdir: process.cwd() } as AppConfig
    );

    return {
      available: testResult.success,
      version: 'macOS Seatbelt',
      features: ['filesystem_restriction', 'network_restriction', 'process_restriction']
    };
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get macOS version information
 */
export function getMacOSVersion(): string | null {
  try {
    const fs = require('fs');
    const versionPlist = fs.readFileSync('/System/Library/CoreServices/SystemVersion.plist', 'utf8');
    const versionMatch = versionPlist.match(/<key>ProductVersion<\/key>\s*<string>([^<]+)<\/string>/);
    return versionMatch ? versionMatch[1] : null;
  } catch (error) {
    return null;
  }
}

/**
 * Check if System Integrity Protection (SIP) is enabled
 */
export function isSIPEnabled(): boolean {
  try {
    const { execSync } = require('child_process');
    const output = execSync('csrutil status', { encoding: 'utf8' });
    return output.includes('enabled');
  } catch (error) {
    // If we can't check, assume it's enabled for safety
    return true;
  }
}
