import * as os from 'os';
/**
 * Command mappings from Unix to Windows
 */
const UNIX_TO_WINDOWS_COMMANDS = {
    'ls': 'dir',
    'cat': 'type',
    'grep': 'findstr',
    'rm': 'del',
    'mv': 'move',
    'cp': 'copy',
    'mkdir': 'md',
    'rmdir': 'rd',
    'pwd': 'cd',
    'which': 'where',
    'ps': 'tasklist',
    'kill': 'taskkill',
    'clear': 'cls',
    'head': 'more',
    'tail': 'more',
    'touch': 'echo.',
    'chmod': 'attrib',
    'df': 'fsutil volume diskfree',
    'du': 'dir /s',
    'find': 'dir /s /b',
    'wc': 'find /c /v ""'
};
/**
 * Command mappings from Windows to Unix
 */
const WINDOWS_TO_UNIX_COMMANDS = {
    'dir': 'ls',
    'type': 'cat',
    'findstr': 'grep',
    'del': 'rm',
    'move': 'mv',
    'copy': 'cp',
    'md': 'mkdir',
    'rd': 'rmdir',
    'where': 'which',
    'tasklist': 'ps',
    'taskkill': 'kill',
    'cls': 'clear',
    'more': 'head',
    'attrib': 'chmod'
};
/**
 * Get current platform
 */
export function getCurrentPlatform() {
    return os.platform() === 'win32' ? 'windows' : 'unix';
}
/**
 * Adapt command for current platform
 */
export function adaptCommand(command) {
    if (command.length === 0)
        return command;
    const platform = getCurrentPlatform();
    const [cmd] = command;
    const lowerCmd = cmd.toLowerCase();
    // If already correct for platform, return as-is
    if (platform === 'windows' && isWindowsCommand(lowerCmd)) {
        return command;
    }
    if (platform === 'unix' && isUnixCommand(lowerCmd)) {
        return command;
    }
    // Adapt command for target platform
    if (platform === 'windows') {
        return adaptToWindows(command);
    }
    else {
        return adaptToUnix(command);
    }
}
/**
 * Adapt Unix command to Windows
 */
function adaptToWindows(command) {
    const [cmd, ...args] = command;
    const lowerCmd = cmd.toLowerCase();
    // Get Windows equivalent
    const windowsCmd = UNIX_TO_WINDOWS_COMMANDS[lowerCmd];
    if (!windowsCmd) {
        // If no mapping exists, return original command
        return command;
    }
    // Handle special cases
    switch (lowerCmd) {
        case 'ls':
            return adaptLsToDir(args);
        case 'cat':
            return adaptCatToType(args);
        case 'grep':
            return adaptGrepToFindstr(args);
        case 'rm':
            return adaptRmToDel(args);
        case 'mv':
            return adaptMvToMove(args);
        case 'cp':
            return adaptCpToCopy(args);
        case 'touch':
            return adaptTouchToEcho(args);
        case 'pwd':
            return ['cd'];
        case 'which':
            return ['where', ...args];
        default:
            return [windowsCmd, ...args];
    }
}
/**
 * Adapt Windows command to Unix
 */
function adaptToUnix(command) {
    const [cmd, ...args] = command;
    const lowerCmd = cmd.toLowerCase();
    // Get Unix equivalent
    const unixCmd = WINDOWS_TO_UNIX_COMMANDS[lowerCmd];
    if (!unixCmd) {
        return command;
    }
    // Handle special cases
    switch (lowerCmd) {
        case 'dir':
            return adaptDirToLs(args);
        case 'type':
            return adaptTypeTocat(args);
        case 'findstr':
            return adaptFindstrToGrep(args);
        case 'del':
            return adaptDelToRm(args);
        case 'move':
            return adaptMoveToMv(args);
        case 'copy':
            return adaptCopyToCp(args);
        default:
            return [unixCmd, ...args];
    }
}
/**
 * Adapt ls to dir
 */
function adaptLsToDir(args) {
    const dirArgs = ['dir'];
    for (const arg of args) {
        if (arg === '-l') {
            // Long format - no direct equivalent, just use default
            continue;
        }
        else if (arg === '-a') {
            dirArgs.push('/a');
        }
        else if (arg === '-la' || arg === '-al') {
            dirArgs.push('/a');
        }
        else if (arg.startsWith('-')) {
            // Skip unknown flags
            continue;
        }
        else {
            // Path argument
            dirArgs.push(arg);
        }
    }
    return dirArgs;
}
/**
 * Adapt cat to type
 */
function adaptCatToType(args) {
    // Filter out Unix-specific flags
    const typeArgs = args.filter(arg => !arg.startsWith('-'));
    return ['type', ...typeArgs];
}
/**
 * Adapt grep to findstr
 */
function adaptGrepToFindstr(args) {
    const findstrArgs = ['findstr'];
    let pattern = '';
    const files = [];
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        if (arg === '-i') {
            findstrArgs.push('/i');
        }
        else if (arg === '-n') {
            findstrArgs.push('/n');
        }
        else if (arg === '-r') {
            findstrArgs.push('/s');
        }
        else if (!arg.startsWith('-')) {
            if (!pattern) {
                pattern = arg;
            }
            else {
                files.push(arg);
            }
        }
    }
    if (pattern) {
        findstrArgs.push(pattern);
    }
    findstrArgs.push(...files);
    return findstrArgs;
}
/**
 * Adapt rm to del
 */
function adaptRmToDel(args) {
    const delArgs = ['del'];
    for (const arg of args) {
        if (arg === '-r' || arg === '-R') {
            delArgs.push('/s');
        }
        else if (arg === '-f') {
            delArgs.push('/f', '/q');
        }
        else if (arg === '-rf' || arg === '-Rf') {
            delArgs.push('/s', '/f', '/q');
        }
        else if (!arg.startsWith('-')) {
            delArgs.push(arg);
        }
    }
    return delArgs;
}
/**
 * Adapt mv to move
 */
function adaptMvToMove(args) {
    // Move command is similar, just filter flags
    const moveArgs = args.filter(arg => !arg.startsWith('-'));
    return ['move', ...moveArgs];
}
/**
 * Adapt cp to copy
 */
function adaptCpToCopy(args) {
    const copyArgs = ['copy'];
    for (const arg of args) {
        if (arg === '-r' || arg === '-R') {
            // Recursive copy - use xcopy instead
            copyArgs[0] = 'xcopy';
            copyArgs.push('/e');
        }
        else if (!arg.startsWith('-')) {
            copyArgs.push(arg);
        }
    }
    return copyArgs;
}
/**
 * Adapt touch to echo
 */
function adaptTouchToEcho(args) {
    if (args.length === 0)
        return ['echo.'];
    // Create empty file using echo
    const filename = args[args.length - 1];
    return ['echo.', '>', filename];
}
/**
 * Adapt dir to ls (reverse direction)
 */
function adaptDirToLs(args) {
    const lsArgs = ['ls'];
    for (const arg of args) {
        if (arg === '/a') {
            lsArgs.push('-a');
        }
        else if (arg === '/l') {
            lsArgs.push('-l');
        }
        else if (arg === '/s') {
            lsArgs.push('-R');
        }
        else if (!arg.startsWith('/')) {
            lsArgs.push(arg);
        }
    }
    return lsArgs;
}
/**
 * Adapt type to cat
 */
function adaptTypeTocat(args) {
    return ['cat', ...args];
}
/**
 * Adapt findstr to grep
 */
function adaptFindstrToGrep(args) {
    const grepArgs = ['grep'];
    for (const arg of args) {
        if (arg === '/i') {
            grepArgs.push('-i');
        }
        else if (arg === '/n') {
            grepArgs.push('-n');
        }
        else if (arg === '/s') {
            grepArgs.push('-r');
        }
        else if (!arg.startsWith('/')) {
            grepArgs.push(arg);
        }
    }
    return grepArgs;
}
/**
 * Adapt del to rm
 */
function adaptDelToRm(args) {
    const rmArgs = ['rm'];
    for (const arg of args) {
        if (arg === '/s') {
            rmArgs.push('-r');
        }
        else if (arg === '/f' || arg === '/q') {
            rmArgs.push('-f');
        }
        else if (!arg.startsWith('/')) {
            rmArgs.push(arg);
        }
    }
    return rmArgs;
}
/**
 * Adapt move to mv
 */
function adaptMoveToMv(args) {
    return ['mv', ...args];
}
/**
 * Adapt copy to cp
 */
function adaptCopyToCp(args) {
    return ['cp', ...args];
}
/**
 * Check if command is a Windows command
 */
function isWindowsCommand(cmd) {
    return Object.values(UNIX_TO_WINDOWS_COMMANDS).includes(cmd) ||
        ['cmd', 'powershell', 'dir', 'type', 'findstr', 'del', 'move', 'copy'].includes(cmd);
}
/**
 * Check if command is a Unix command
 */
function isUnixCommand(cmd) {
    return Object.keys(UNIX_TO_WINDOWS_COMMANDS).includes(cmd) ||
        ['bash', 'sh', 'zsh', 'ls', 'cat', 'grep', 'rm', 'mv', 'cp'].includes(cmd);
}
/**
 * Get shell for current platform
 */
export function getDefaultShell() {
    const platform = getCurrentPlatform();
    if (platform === 'windows') {
        return process.env.COMSPEC || 'cmd.exe';
    }
    else {
        return process.env.SHELL || '/bin/bash';
    }
}
/**
 * Get shell arguments for command execution
 */
export function getShellArgs(command) {
    const platform = getCurrentPlatform();
    if (platform === 'windows') {
        return ['/c', command];
    }
    else {
        return ['-c', command];
    }
}
//# sourceMappingURL=platform-commands.js.map