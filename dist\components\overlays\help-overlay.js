import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text, useInput } from 'ink';
export function HelpOverlay({ onClose }) {
    useInput((input, key) => {
        if (key.escape || key.return || input === 'q') {
            onClose();
        }
    });
    return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDCDA Kritrima AI CLI Help" }) }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Slash Commands:" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/help" }), " - Show this help information"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/model" }), " - Switch AI model or provider"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/history" }), " - View conversation history"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/clear" }), " - Clear current conversation"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/approval" }), " - Change command approval mode"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/status" }), " - Show current status and settings"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "/exit" }), " - Exit the application"] })] })] }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Keyboard Shortcuts:" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Ctrl+C" }), " - Exit application"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Ctrl+M" }), " - Open model selection"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Ctrl+H" }), " - Show help (this screen)"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Ctrl+R" }), " - View history"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Esc" }), " - Close overlays"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "\u2191\u2193" }), " - Navigate command history"] }), _jsxs(Text, { children: [_jsx(Text, { color: "blue", children: "Tab" }), " - Autocomplete commands/files"] })] })] }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "File References:" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsxs(Text, { children: ["Use ", _jsx(Text, { color: "green", children: "@filename" }), " to reference files in your messages"] }), _jsxs(Text, { children: ["Example: \"Analyze the code in ", _jsx(Text, { color: "green", children: "@src/app.tsx" }), "\""] }), _jsx(Text, { children: "Tab completion works for file paths after @" })] })] }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Approval Modes:" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsxs(Text, { children: [_jsx(Text, { color: "red", children: "suggest" }), " - Manual approval for all commands"] }), _jsxs(Text, { children: [_jsx(Text, { color: "yellow", children: "auto-edit" }), " - Auto-approve safe commands, ask for others"] }), _jsxs(Text, { children: [_jsx(Text, { color: "green", children: "full-auto" }), " - Auto-approve all commands (use with caution)"] })] })] }), _jsxs(Box, { flexDirection: "column", marginBottom: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Tips:" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsx(Text, { children: "\u2022 The AI can execute shell commands and modify files" }), _jsx(Text, { children: "\u2022 Commands are sandboxed for security when possible" }), _jsx(Text, { children: "\u2022 Use descriptive messages for better AI responses" }), _jsx(Text, { children: "\u2022 Check approval mode if commands aren't executing" }), _jsx(Text, { children: "\u2022 Git integration provides better context awareness" })] })] }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "gray", children: "Press Esc, Enter, or 'q' to close this help screen" }) })] }));
}
//# sourceMappingURL=help-overlay.js.map