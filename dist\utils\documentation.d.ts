import { AppConfig } from '../types';
/**
 * Documentation types
 */
export type DocumentationType = 'api' | 'readme' | 'changelog' | 'architecture' | 'deployment' | 'user-guide' | 'developer-guide';
/**
 * Documentation section
 */
export interface DocumentationSection {
    title: string;
    content: string;
    level: number;
    subsections?: DocumentationSection[];
}
/**
 * API documentation item
 */
export interface APIDocItem {
    name: string;
    type: 'function' | 'class' | 'interface' | 'type' | 'constant';
    description: string;
    parameters?: Array<{
        name: string;
        type: string;
        description: string;
        optional?: boolean;
    }>;
    returns?: {
        type: string;
        description: string;
    };
    examples?: string[];
    deprecated?: boolean;
    since?: string;
}
/**
 * Documentation generator options
 */
export interface DocumentationOptions {
    outputDir: string;
    format: 'markdown' | 'html' | 'json';
    includePrivate: boolean;
    includeExamples: boolean;
    includeSourceLinks: boolean;
    template?: string;
    customSections?: DocumentationSection[];
}
/**
 * Documentation generator class
 */
export declare class DocumentationGenerator {
    private fileOps;
    private config;
    constructor(config: AppConfig);
    /**
     * Generate complete project documentation
     */
    generateDocumentation(options: DocumentationOptions): Promise<void>;
    /**
     * Generate README.md
     */
    generateReadme(options: DocumentationOptions): Promise<void>;
    /**
     * Build README content
     */
    private buildReadmeContent;
    /**
     * Generate API documentation
     */
    generateAPIDocumentation(options: DocumentationOptions): Promise<void>;
    /**
     * Extract API documentation from file
     */
    private extractAPIDocumentation;
    /**
     * Parse JSDoc comment
     */
    private parseJSDocComment;
    /**
     * Build API documentation content
     */
    private buildAPIDocumentation;
    /**
     * Generate architecture documentation
     */
    generateArchitectureDoc(options: DocumentationOptions): Promise<void>;
    /**
     * Generate user guide
     */
    generateUserGuide(options: DocumentationOptions): Promise<void>;
    /**
     * Generate developer guide
     */
    generateDeveloperGuide(options: DocumentationOptions): Promise<void>;
    /**
     * Generate changelog
     */
    generateChangelog(options: DocumentationOptions): Promise<void>;
    /**
     * Generate directory tree
     */
    private generateDirectoryTree;
    /**
     * Format file size
     */
    private formatFileSize;
}
//# sourceMappingURL=documentation.d.ts.map