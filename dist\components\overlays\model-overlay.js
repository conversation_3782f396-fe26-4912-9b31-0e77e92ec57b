import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { getProviderNames, getProviderModels } from '../../utils/providers.js';
import { fetchModels } from '../../utils/model-utils.js';
export function ModelOverlay({ currentModel, currentProvider, onModelChange, onClose }) {
    const [activeTab, setActiveTab] = useState('providers');
    const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
    const [selectedModelIndex, setSelectedModelIndex] = useState(0);
    const [providers] = useState(getProviderNames());
    const [models, setModels] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    // Initialize selected provider index
    useEffect(() => {
        const currentIndex = providers.indexOf(currentProvider);
        if (currentIndex !== -1) {
            setSelectedProviderIndex(currentIndex);
        }
    }, [providers, currentProvider]);
    // Load models when provider changes
    useEffect(() => {
        loadModelsForProvider(providers[selectedProviderIndex]);
    }, [selectedProviderIndex, providers]);
    const loadModelsForProvider = async (provider) => {
        setLoading(true);
        setError(null);
        try {
            // Try to fetch models from API
            const fetchedModels = await fetchModels(provider);
            setModels(fetchedModels);
            // Set selected model index
            const currentIndex = fetchedModels.indexOf(currentModel);
            setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
        }
        catch (err) {
            // Fallback to predefined models
            const fallbackModels = getProviderModels(provider);
            setModels(fallbackModels);
            setError('Failed to fetch models from API, showing predefined models');
            const currentIndex = fallbackModels.indexOf(currentModel);
            setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
        }
        finally {
            setLoading(false);
        }
    };
    useInput((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.tab) {
            setActiveTab(prev => prev === 'providers' ? 'models' : 'providers');
            return;
        }
        if (key.return) {
            if (activeTab === 'providers') {
                setActiveTab('models');
            }
            else {
                // Select model
                const selectedProvider = providers[selectedProviderIndex];
                const selectedModel = models[selectedModelIndex];
                if (selectedModel) {
                    onModelChange(selectedModel, selectedProvider);
                }
            }
            return;
        }
        if (key.upArrow) {
            if (activeTab === 'providers') {
                setSelectedProviderIndex(prev => prev > 0 ? prev - 1 : providers.length - 1);
            }
            else {
                setSelectedModelIndex(prev => prev > 0 ? prev - 1 : models.length - 1);
            }
            return;
        }
        if (key.downArrow) {
            if (activeTab === 'providers') {
                setSelectedProviderIndex(prev => prev < providers.length - 1 ? prev + 1 : 0);
            }
            else {
                setSelectedModelIndex(prev => prev < models.length - 1 ? prev + 1 : 0);
            }
            return;
        }
    });
    return (_jsxs(Box, { flexDirection: "column", height: "100%", padding: 2, children: [_jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: "cyan", bold: true, children: "\uD83E\uDD16 Model & Provider Selection" }) }), _jsxs(Box, { marginBottom: 2, children: [_jsx(Box, { marginRight: 4, children: _jsxs(Text, { color: activeTab === 'providers' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'providers' ? '▶ ' : '  ', "Providers"] }) }), _jsx(Box, { children: _jsxs(Text, { color: activeTab === 'models' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'models' ? '▶ ' : '  ', "Models"] }) })] }), _jsxs(Box, { flexDirection: "row", flexGrow: 1, children: [_jsxs(Box, { flexDirection: "column", width: "50%", marginRight: 2, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "Providers:" }) }), providers.map((provider, index) => (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: index === selectedProviderIndex
                                        ? (activeTab === 'providers' ? 'cyan' : 'blue')
                                        : 'gray', children: [index === selectedProviderIndex ? '▶ ' : '  ', provider, provider === currentProvider && ' (current)'] }) }, provider)))] }), _jsxs(Box, { flexDirection: "column", width: "50%", children: [_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: "yellow", bold: true, children: ["Models for ", providers[selectedProviderIndex], ":"] }) }), loading && (_jsx(Text, { color: "blue", children: "Loading models..." })), error && (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: "red", children: ["\u26A0\uFE0F ", error] }) })), !loading && models.length === 0 && (_jsx(Text, { color: "gray", children: "No models available" })), !loading && models.map((model, index) => (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: index === selectedModelIndex
                                        ? (activeTab === 'models' ? 'cyan' : 'blue')
                                        : 'gray', children: [index === selectedModelIndex ? '▶ ' : '  ', model, model === currentModel && providers[selectedProviderIndex] === currentProvider && ' (current)'] }) }, model)))] })] }), _jsxs(Box, { marginTop: 2, flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "Use \u2191\u2193 to navigate, Tab to switch tabs, Enter to select, Esc to close" }), _jsxs(Text, { color: "gray", dimColor: true, children: ["Current: ", currentModel, " (", currentProvider, ")"] })] })] }));
}
//# sourceMappingURL=model-overlay.js.map