import { AppConfig } from '../types';
/**
 * File operation types
 */
export type FileOperationType = 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'search' | 'analyze';
/**
 * File metadata
 */
export interface FileMetadata {
    path: string;
    name: string;
    extension: string;
    size: number;
    created: Date;
    modified: Date;
    isDirectory: boolean;
    isFile: boolean;
    permissions: {
        readable: boolean;
        writable: boolean;
        executable: boolean;
    };
    mimeType?: string;
    encoding?: string;
}
/**
 * File search options
 */
export interface FileSearchOptions {
    pattern?: string;
    extensions?: string[];
    includeHidden?: boolean;
    maxDepth?: number;
    maxResults?: number;
    caseSensitive?: boolean;
    regex?: boolean;
    content?: string;
    modifiedAfter?: Date;
    modifiedBefore?: Date;
    sizeMin?: number;
    sizeMax?: number;
}
/**
 * File analysis result
 */
export interface FileAnalysis {
    path: string;
    language?: string;
    lineCount: number;
    characterCount: number;
    wordCount: number;
    complexity?: number;
    dependencies?: string[];
    exports?: string[];
    imports?: string[];
    functions?: string[];
    classes?: string[];
    comments?: number;
    documentation?: string;
}
/**
 * Project structure
 */
export interface ProjectStructure {
    root: string;
    files: FileMetadata[];
    directories: string[];
    languages: Record<string, number>;
    totalFiles: number;
    totalSize: number;
    structure: any;
}
/**
 * Advanced file operations manager
 */
export declare class FileOperationsManager {
    private config;
    private allowedPaths;
    constructor(config: AppConfig);
    /**
     * Validate file path is within allowed directories
     */
    private validatePath;
    /**
     * Get file metadata
     */
    getFileMetadata(filePath: string): Promise<FileMetadata | null>;
    /**
     * Search files with advanced options
     */
    searchFiles(searchOptions?: FileSearchOptions): Promise<FileMetadata[]>;
    /**
     * Analyze file content
     */
    analyzeFile(filePath: string): Promise<FileAnalysis | null>;
    /**
     * Detect programming language
     */
    private detectLanguage;
    /**
     * Perform language-specific analysis
     */
    private performLanguageSpecificAnalysis;
    /**
     * Analyze JavaScript/TypeScript files
     */
    private analyzeJavaScript;
    /**
     * Analyze Python files
     */
    private analyzePython;
    /**
     * Analyze Java files
     */
    private analyzeJava;
    /**
     * Generic analysis for unknown languages
     */
    private analyzeGeneric;
    /**
     * Get project structure
     */
    getProjectStructure(): Promise<ProjectStructure>;
    /**
     * Create file backup
     */
    createBackup(filePath: string): Promise<string | null>;
    /**
     * Restore file from backup
     */
    restoreFromBackup(originalPath: string, backupPath: string): Promise<boolean>;
    /**
     * Clean up old backups
     */
    cleanupBackups(olderThanDays?: number): Promise<number>;
}
//# sourceMappingURL=file-operations.d.ts.map