import * as fs from 'fs-extra';
import { promises as fsPromises } from 'fs';
import * as path from 'path';
import { FileOperationsManager, FileAnalysis, ProjectStructure } from './file-operations';
import { AppConfig } from '../types';

/**
 * Documentation types
 */
export type DocumentationType = 
  | 'api'
  | 'readme'
  | 'changelog'
  | 'architecture'
  | 'deployment'
  | 'user-guide'
  | 'developer-guide';

/**
 * Documentation section
 */
export interface DocumentationSection {
  title: string;
  content: string;
  level: number;
  subsections?: DocumentationSection[];
}

/**
 * API documentation item
 */
export interface APIDocItem {
  name: string;
  type: 'function' | 'class' | 'interface' | 'type' | 'constant';
  description: string;
  parameters?: Array<{
    name: string;
    type: string;
    description: string;
    optional?: boolean;
  }>;
  returns?: {
    type: string;
    description: string;
  };
  examples?: string[];
  deprecated?: boolean;
  since?: string;
}

/**
 * Documentation generator options
 */
export interface DocumentationOptions {
  outputDir: string;
  format: 'markdown' | 'html' | 'json';
  includePrivate: boolean;
  includeExamples: boolean;
  includeSourceLinks: boolean;
  template?: string;
  customSections?: DocumentationSection[];
}

/**
 * Documentation generator class
 */
export class DocumentationGenerator {
  private fileOps: FileOperationsManager;
  private config: AppConfig;

  constructor(config: AppConfig) {
    this.config = config;
    this.fileOps = new FileOperationsManager(config);
  }

  /**
   * Generate complete project documentation
   */
  async generateDocumentation(options: DocumentationOptions): Promise<void> {
    try {
      await fs.ensureDir(options.outputDir);

      // Generate different types of documentation
      await this.generateReadme(options);
      await this.generateAPIDocumentation(options);
      await this.generateArchitectureDoc(options);
      await this.generateUserGuide(options);
      await this.generateDeveloperGuide(options);
      await this.generateChangelog(options);

      console.log(`Documentation generated in ${options.outputDir}`);
    } catch (error) {
      console.error('Failed to generate documentation:', error);
      throw error;
    }
  }

  /**
   * Generate README.md
   */
  async generateReadme(options: DocumentationOptions): Promise<void> {
    const projectStructure = await this.fileOps.getProjectStructure();
    const packageJsonPath = path.join(projectStructure.root, 'package.json');
    
    let packageInfo: any = {};
    try {
      const packageData = await fsPromises.readFile(packageJsonPath, 'utf-8');
      packageInfo = JSON.parse(packageData);
    } catch {
      // Package.json not found, use defaults
    }

    const readme = this.buildReadmeContent(projectStructure, packageInfo);
    const outputPath = path.join(options.outputDir, 'README.md');
    
    await fs.outputFile(outputPath, readme, 'utf-8');
  }

  /**
   * Build README content
   */
  private buildReadmeContent(structure: ProjectStructure, packageInfo: any): string {
    const name = packageInfo.name || path.basename(structure.root);
    const description = packageInfo.description || 'A software project';
    const version = packageInfo.version || '1.0.0';

    let content = `# ${name}\n\n`;
    content += `${description}\n\n`;
    content += `**Version:** ${version}\n\n`;

    // Table of contents
    content += '## Table of Contents\n\n';
    content += '- [Installation](#installation)\n';
    content += '- [Usage](#usage)\n';
    content += '- [Features](#features)\n';
    content += '- [Project Structure](#project-structure)\n';
    content += '- [Contributing](#contributing)\n';
    content += '- [License](#license)\n\n';

    // Installation
    content += '## Installation\n\n';
    if (packageInfo.scripts) {
      content += '```bash\n';
      content += 'npm install\n';
      content += '```\n\n';
    }

    // Usage
    content += '## Usage\n\n';
    if (packageInfo.scripts) {
      content += '### Development\n\n';
      content += '```bash\n';
      if (packageInfo.scripts.dev) {
        content += 'npm run dev\n';
      }
      if (packageInfo.scripts.build) {
        content += 'npm run build\n';
      }
      if (packageInfo.scripts.test) {
        content += 'npm test\n';
      }
      content += '```\n\n';
    }

    // Features
    content += '## Features\n\n';
    const languages = Object.keys(structure.languages);
    if (languages.length > 0) {
      content += `- Built with ${languages.join(', ')}\n`;
    }
    content += `- ${structure.totalFiles} files\n`;
    content += `- ${this.formatFileSize(structure.totalSize)} total size\n\n`;

    // Project structure
    content += '## Project Structure\n\n';
    content += '```\n';
    content += this.generateDirectoryTree(structure);
    content += '```\n\n';

    // Languages breakdown
    if (Object.keys(structure.languages).length > 0) {
      content += '### Languages\n\n';
      Object.entries(structure.languages)
        .sort(([,a], [,b]) => b - a)
        .forEach(([lang, count]) => {
          content += `- **${lang}**: ${count} files\n`;
        });
      content += '\n';
    }

    // Contributing
    content += '## Contributing\n\n';
    content += '1. Fork the repository\n';
    content += '2. Create a feature branch\n';
    content += '3. Make your changes\n';
    content += '4. Add tests if applicable\n';
    content += '5. Submit a pull request\n\n';

    // License
    content += '## License\n\n';
    content += `${packageInfo.license || 'MIT'} License\n\n`;

    return content;
  }

  /**
   * Generate API documentation
   */
  async generateAPIDocumentation(options: DocumentationOptions): Promise<void> {
    const files = await this.fileOps.searchFiles({
      extensions: ['.ts', '.js', '.tsx', '.jsx'],
      maxResults: 1000
    });

    const apiDocs: APIDocItem[] = [];

    for (const file of files) {
      const analysis = await this.fileOps.analyzeFile(file.path);
      if (analysis) {
        const docs = await this.extractAPIDocumentation(file.path, analysis);
        apiDocs.push(...docs);
      }
    }

    const content = this.buildAPIDocumentation(apiDocs);
    const outputPath = path.join(options.outputDir, 'API.md');
    
    await fs.outputFile(outputPath, content, 'utf-8');
  }

  /**
   * Extract API documentation from file
   */
  private async extractAPIDocumentation(filePath: string, analysis: FileAnalysis): Promise<APIDocItem[]> {
    const docs: APIDocItem[] = [];

    try {
      const content = await fsPromises.readFile(filePath, 'utf-8');
      
      // Extract functions with JSDoc comments
      const functionRegex = /\/\*\*([\s\S]*?)\*\/\s*(?:export\s+)?(?:async\s+)?function\s+(\w+)/g;
      let match;

      while ((match = functionRegex.exec(content)) !== null) {
        const [, comment, functionName] = match;
        const doc = this.parseJSDocComment(comment, functionName, 'function');
        if (doc) {
          docs.push(doc);
        }
      }

      // Extract classes
      const classRegex = /\/\*\*([\s\S]*?)\*\/\s*(?:export\s+)?class\s+(\w+)/g;
      while ((match = classRegex.exec(content)) !== null) {
        const [, comment, className] = match;
        const doc = this.parseJSDocComment(comment, className, 'class');
        if (doc) {
          docs.push(doc);
        }
      }

    } catch (error) {
      console.warn(`Failed to extract API docs from ${filePath}:`, error);
    }

    return docs;
  }

  /**
   * Parse JSDoc comment
   */
  private parseJSDocComment(comment: string, name: string, type: APIDocItem['type']): APIDocItem | null {
    try {
      const lines = comment.split('\n').map(line => line.replace(/^\s*\*\s?/, '').trim());
      
      let description = '';
      const parameters: APIDocItem['parameters'] = [];
      let returns: APIDocItem['returns'];
      const examples: string[] = [];
      let deprecated = false;
      let since: string | undefined;

      let currentSection = 'description';
      let exampleContent = '';

      for (const line of lines) {
        if (line.startsWith('@param')) {
          currentSection = 'param';
          const paramMatch = line.match(/@param\s+\{([^}]+)\}\s+(\w+)\s*(.*)/);
          if (paramMatch) {
            parameters.push({
              name: paramMatch[2],
              type: paramMatch[1],
              description: paramMatch[3] || '',
              optional: paramMatch[1].includes('?') || line.includes('[')
            });
          }
        } else if (line.startsWith('@returns') || line.startsWith('@return')) {
          currentSection = 'returns';
          const returnMatch = line.match(/@returns?\s+\{([^}]+)\}\s*(.*)/);
          if (returnMatch) {
            returns = {
              type: returnMatch[1],
              description: returnMatch[2] || ''
            };
          }
        } else if (line.startsWith('@example')) {
          currentSection = 'example';
          exampleContent = '';
        } else if (line.startsWith('@deprecated')) {
          deprecated = true;
        } else if (line.startsWith('@since')) {
          since = line.replace('@since', '').trim();
        } else if (currentSection === 'description' && line) {
          description += (description ? ' ' : '') + line;
        } else if (currentSection === 'example') {
          exampleContent += line + '\n';
        }
      }

      if (exampleContent.trim()) {
        examples.push(exampleContent.trim());
      }

      return {
        name,
        type,
        description,
        parameters: parameters.length > 0 ? parameters : undefined,
        returns,
        examples: examples.length > 0 ? examples : undefined,
        deprecated,
        since
      };
    } catch (error) {
      console.warn(`Failed to parse JSDoc comment for ${name}:`, error);
      return null;
    }
  }

  /**
   * Build API documentation content
   */
  private buildAPIDocumentation(apiDocs: APIDocItem[]): string {
    let content = '# API Documentation\n\n';

    // Group by type
    const grouped = apiDocs.reduce((acc, doc) => {
      if (!acc[doc.type]) acc[doc.type] = [];
      acc[doc.type].push(doc);
      return acc;
    }, {} as Record<string, APIDocItem[]>);

    // Generate sections for each type
    Object.entries(grouped).forEach(([type, docs]) => {
      content += `## ${type.charAt(0).toUpperCase() + type.slice(1)}s\n\n`;

      docs.forEach(doc => {
        content += `### ${doc.name}\n\n`;
        
        if (doc.deprecated) {
          content += '> **Deprecated**\n\n';
        }

        content += `${doc.description}\n\n`;

        if (doc.parameters && doc.parameters.length > 0) {
          content += '**Parameters:**\n\n';
          doc.parameters.forEach(param => {
            const optional = param.optional ? ' (optional)' : '';
            content += `- \`${param.name}\` (\`${param.type}\`)${optional}: ${param.description}\n`;
          });
          content += '\n';
        }

        if (doc.returns) {
          content += `**Returns:** \`${doc.returns.type}\` - ${doc.returns.description}\n\n`;
        }

        if (doc.examples && doc.examples.length > 0) {
          content += '**Example:**\n\n';
          doc.examples.forEach(example => {
            content += '```javascript\n';
            content += example;
            content += '\n```\n\n';
          });
        }

        if (doc.since) {
          content += `*Since: ${doc.since}*\n\n`;
        }

        content += '---\n\n';
      });
    });

    return content;
  }

  /**
   * Generate architecture documentation
   */
  async generateArchitectureDoc(options: DocumentationOptions): Promise<void> {
    const structure = await this.fileOps.getProjectStructure();
    
    let content = '# Architecture Documentation\n\n';
    content += '## Overview\n\n';
    content += 'This document describes the architecture and design of the project.\n\n';

    content += '## Directory Structure\n\n';
    content += '```\n';
    content += this.generateDirectoryTree(structure);
    content += '```\n\n';

    content += '## Components\n\n';
    // Add component descriptions based on directory structure
    structure.directories.forEach(dir => {
      const relativePath = path.relative(structure.root, dir);
      if (relativePath.includes('components') || relativePath.includes('modules')) {
        content += `### ${path.basename(dir)}\n\n`;
        content += `Location: \`${relativePath}\`\n\n`;
        content += 'Description: [Add component description]\n\n';
      }
    });

    const outputPath = path.join(options.outputDir, 'ARCHITECTURE.md');
    await fs.writeFile(outputPath, content, 'utf-8');
  }

  /**
   * Generate user guide
   */
  async generateUserGuide(options: DocumentationOptions): Promise<void> {
    let content = '# User Guide\n\n';
    content += '## Getting Started\n\n';
    content += 'This guide will help you get started with using the application.\n\n';

    content += '## Installation\n\n';
    content += '1. Download the application\n';
    content += '2. Follow the installation instructions\n';
    content += '3. Configure your settings\n\n';

    content += '## Basic Usage\n\n';
    content += '[Add usage instructions]\n\n';

    content += '## Advanced Features\n\n';
    content += '[Add advanced feature descriptions]\n\n';

    content += '## Troubleshooting\n\n';
    content += '[Add common issues and solutions]\n\n';

    const outputPath = path.join(options.outputDir, 'USER_GUIDE.md');
    await fs.writeFile(outputPath, content, 'utf-8');
  }

  /**
   * Generate developer guide
   */
  async generateDeveloperGuide(options: DocumentationOptions): Promise<void> {
    let content = '# Developer Guide\n\n';
    content += '## Development Setup\n\n';
    content += '1. Clone the repository\n';
    content += '2. Install dependencies\n';
    content += '3. Set up development environment\n\n';

    content += '## Building\n\n';
    content += '[Add build instructions]\n\n';

    content += '## Testing\n\n';
    content += '[Add testing instructions]\n\n';

    content += '## Contributing\n\n';
    content += '[Add contribution guidelines]\n\n';

    const outputPath = path.join(options.outputDir, 'DEVELOPER_GUIDE.md');
    await fs.writeFile(outputPath, content, 'utf-8');
  }

  /**
   * Generate changelog
   */
  async generateChangelog(options: DocumentationOptions): Promise<void> {
    let content = '# Changelog\n\n';
    content += 'All notable changes to this project will be documented in this file.\n\n';

    content += '## [Unreleased]\n\n';
    content += '### Added\n';
    content += '- New features\n\n';

    content += '### Changed\n';
    content += '- Changes in existing functionality\n\n';

    content += '### Deprecated\n';
    content += '- Soon-to-be removed features\n\n';

    content += '### Removed\n';
    content += '- Removed features\n\n';

    content += '### Fixed\n';
    content += '- Bug fixes\n\n';

    content += '### Security\n';
    content += '- Security improvements\n\n';

    const outputPath = path.join(options.outputDir, 'CHANGELOG.md');
    await fs.writeFile(outputPath, content, 'utf-8');
  }

  /**
   * Generate directory tree
   */
  private generateDirectoryTree(structure: ProjectStructure): string {
    // Simplified tree generation
    const tree: string[] = [];
    const rootName = path.basename(structure.root);
    tree.push(`${rootName}/`);

    // Add main directories
    const mainDirs = structure.directories
      .filter(dir => path.dirname(dir) === structure.root)
      .map(dir => path.basename(dir))
      .sort();

    mainDirs.forEach((dir, index) => {
      const isLast = index === mainDirs.length - 1;
      tree.push(`${isLast ? '└── ' : '├── '}${dir}/`);
    });

    return tree.join('\n');
  }

  /**
   * Format file size
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
  }
}
