import { ExecResult, AppConfig, ApprovalPolicy, ResponseInputItem } from '../../types/index.js';
export interface ExecCommandResult {
    outputText: string;
    metadata: Record<string, any>;
    additionalItems: ResponseInputItem[];
}
/**
 * Handle execution of shell commands with approval workflow
 */
export declare function handleExecCommand(args: {
    command: string[];
    workdir?: string;
    timeout?: number;
}, config: AppConfig, approvalPolicy: ApprovalPolicy, additionalWritableRoots?: ReadonlyArray<string>, getCommandConfirmation?: (request: any) => Promise<boolean>, signal?: AbortSignal): Promise<ExecCommandResult>;
/**
 * Parse shell command from string
 */
export declare function parseShellCommand(commandString: string): string[];
/**
 * Get command execution summary
 */
export declare function getExecutionSummary(result: ExecResult): string;
/**
 * Check if command is likely to produce large output
 */
export declare function isLargeOutputCommand(command: string[]): boolean;
/**
 * Suggest command optimizations
 */
export declare function suggestCommandOptimizations(command: string[]): string[];
/**
 * Estimate command execution time
 */
export declare function estimateExecutionTime(command: string[]): 'fast' | 'medium' | 'slow';
/**
 * Get command help text
 */
export declare function getCommandHelp(command: string): string;
//# sourceMappingURL=handle-exec-command.d.ts.map